- id: '1680064042932'
  alias: 'Status - Volume - Night '
  description: ''
  triggers:
  - at: 07:00:00
    trigger: time
    id: Full
  - at: '19:00:00'
    trigger: time
    id: Medium
  - at: '21:00:00'
    trigger: time
    id: Medium
  - at: '22:00:00'
    trigger: time
    id: Medium
  - at: '23:00:00'
    trigger: time
    id: Low
  conditions: []
  actions:
  - alias: full
    if:
    - condition: trigger
      id:
      - Full
    then:
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.8
      target:
        area_id:
        - living_room
        - play_room
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.6
      target:
        entity_id: media_player.bathroom_speaker
  - alias: Medium
    if:
    - condition: trigger
      id:
      - Medium
    then:
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.5
      target:
        area_id:
        - living_room
        - play_room
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.4
      target:
        entity_id: media_player.bathroom_speaker
  - alias: Low
    if:
    - condition: trigger
      id:
      - Low
    then:
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.4
      target:
        area_id:
        - play_room
        - living_room
    - action: media_player.volume_set
      metadata: {}
      data:
        volume_level: 0.3
      target:
        entity_id: media_player.bathroom_speaker
  mode: single
- id: '1680972069777'
  alias: Control - Leave - Energy Off
  description: ''
  triggers:
  - entity_id:
    - input_boolean.location_daniel_home
    to: 'off'
    for:
      hours: 0
      minutes: 0
      seconds: 0
    trigger: state
  conditions: []
  actions:
  - parallel:
    - data: {}
      action: script.blackouts_down
    - data: {}
      action: script.music_mode_off
    - data: {}
      action: script.moviemode_off
    - metadata: {}
      data: {}
      target:
        entity_id: script.work_mode_on
      action: script.turn_on
    - metadata: {}
      data: {}
      target:
        area_id:
        - balcony
        - main_bathroom
        - clothes_zone
        - bedroom
        - play_room
        - kitchen
        - living_room
        - studio
      action: light.turn_off
    - metadata: {}
      data: {}
      target:
        entity_id:
        - fan.games_fan
        - fan.bedroom_purifier
        - fan.games_purifier
        - fan.bedroom_fan
      action: fan.turn_off
    - action: script.notify_phone
      metadata: {}
      data:
        title: State
        message: Te fuiste
  - data: {}
    target:
      entity_id: switch.10_0_0_25_motion_detection
    enabled: false
    action: switch.turn_on
  mode: single
- id: '1692751896210'
  alias: Status - Door - Balcony - Opened
  description: ''
  trigger:
  - type: opened
    platform: device
    device_id: 37411987c5d0b69b22eb7db9fa66ffa0
    entity_id: ec6632fe5f125e3d8e2cfcf50d3da82f
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.ventana
  mode: single
- id: '1692751958057'
  alias: Status - Door - Balcony - Closed
  description: ''
  trigger:
  - type: not_opened
    platform: device
    device_id: 37411987c5d0b69b22eb7db9fa66ffa0
    entity_id: ec6632fe5f125e3d8e2cfcf50d3da82f
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.ventana
  mode: single
- id: '1692752245944'
  alias: Status - Window - Kitchen - Closed
  description: ''
  trigger:
  - type: opened
    platform: device
    device_id: 7a569bbc4a618b80ec9618d368dd1a79
    entity_id: 428520c4bb63b6d5bbc29f282af59d47
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.ventana_ropas
  mode: single
- id: '1692752276257'
  alias: Status - Window - Kitchen - Opened
  description: ''
  trigger:
  - type: not_opened
    platform: device
    device_id: 7a569bbc4a618b80ec9618d368dd1a79
    entity_id: 428520c4bb63b6d5bbc29f282af59d47
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.ventana_ropas
  mode: single
- id: '1693344654462'
  alias: Alert - Battery - Tablet
  description: ''
  triggers:
  - type: battery_level
    device_id: 7bccb6f687937ca398bad460107bcd47
    entity_id: e276c2a9fbeba42f30da96332783c5a5
    domain: sensor
    trigger: device
    below: 30
    for:
      hours: 0
      minutes: 30
      seconds: 0
  conditions: []
  actions:
  - action: script.notify_phone
    data:
      title: Tablet
      message: Battery is down
  mode: single
- id: '1693344888236'
  alias: Control - Chromecast - ON
  description: ''
  trigger:
  - platform: device
    type: turned_on
    device_id: b52e5d8af655c0d1ace2836b2ab7a5cf
    entity_id: 689eea69bd5d08bdf593910a0d8e4565
    domain: remote
  condition: []
  action:
  - service: script.movie_mode_on
    data: {}
  mode: single
- id: '1693344940676'
  alias: Control - Chromecast - OFF
  description: ''
  trigger:
  - platform: device
    type: turned_off
    device_id: b52e5d8af655c0d1ace2836b2ab7a5cf
    entity_id: 689eea69bd5d08bdf593910a0d8e4565
    domain: remote
  condition: []
  action:
  - service: script.moviemode_off
    data: {}
  mode: single
- id: '1693716478218'
  alias: Control - Ventilador - Play - On
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.ventilador
    to: 'on'
  condition: []
  action:
  - service: remote.send_command
    data:
      device: fan_1
      command: power
    target:
      entity_id: remote.rf
  mode: single
- id: '1693716524338'
  alias: Control - Ventilador - Play - Off
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.ventilador
    to: 'off'
  condition: []
  action:
  - service: remote.send_command
    data:
      device: fan_1
      command: power
      num_repeats: 2
      delay_secs: 0.4
    target:
      entity_id: remote.rf
  mode: single
- id: '1693726437976'
  alias: 'Control - Ventilador - Bed - Off '
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.ventilador_bed
    to: 'off'
  condition: []
  action:
  - service: remote.send_command
    data:
      device: fan_2
      command: power
      num_repeats: 2
      delay_secs: 0.4
    target:
      entity_id: remote.ir
  mode: single
- id: '1693726486262'
  alias: Control - Ventilador - Bed - On
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.ventilador_bed
    to: 'on'
  condition: []
  action:
  - service: remote.send_command
    data:
      device: fan_2
      command: power
    target:
      entity_id: remote.ir
  mode: single
- id: '1693727275538'
  alias: Sleep - Control - Humidifer - On
  description: ''
  trigger:
  - platform: time
    at: 01:00:00
  condition: []
  action:
  - type: turn_on
    device_id: cafec9b17d961aa1ca36a901fba58746
    entity_id: 9b1d0d4044e31a5dd845bbb6139ad93c
    domain: light
    brightness_pct: 0
  mode: single
- id: '1694558416564'
  alias: Status - Puerta Principal - Closed
  description: ''
  trigger:
  - type: not_opened
    platform: device
    device_id: 9659289c13a7b4cd00daa3f183d182dc
    entity_id: 26c83c28515daaf69dd8eb850ca2b4e5
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.puerta_principal
  mode: single
- id: '1694558828827'
  alias: Status - Puerta Principal - Opened
  description: ''
  trigger:
  - type: opened
    platform: device
    device_id: 9659289c13a7b4cd00daa3f183d182dc
    entity_id: 26c83c28515daaf69dd8eb850ca2b4e5
    domain: binary_sensor
  condition: []
  action:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.puerta_principal
  mode: single
- id: '1694993669728'
  alias: Alert - Raining
  description: ''
  trigger:
  - platform: state
    entity_id:
    - weather.forecast_home_2
    to: rainy
    for:
      hours: 1
      minutes: 0
      seconds: 0
  condition:
  - condition: or
    conditions:
    - condition: state
      entity_id: input_boolean.ventana_habitacion_principal
      state: 'off'
    - condition: state
      entity_id: input_boolean.ventana_ropas
      state: 'off'
    - condition: state
      entity_id: input_boolean.ventana_juegos
      state: 'off'
  action:
  - action: script.send_push_notification
    data:
      message: "Abierto: {% set off_devices = [] %}\n    {% if is_state('input_boolean.ventana_habitacion_principal',
        'off') %}\n      {% set off_devices = off_devices + ['Ventana Habitacion Principal']
        %}\n    {% endif %}\n    {% if is_state('input_boolean.ventana_ropas', 'off')
        %}\n      {% set off_devices = off_devices + ['Ventana Ropas'] %}\n    {%
        endif %}\n    {% if is_state('input_boolean.ventana_juegos', 'off') %}\n      {%
        set off_devices = off_devices + ['Ventana Juegos'] %}\n    {% endif %}\n    {{
        off_devices | join(', ') }}\n"
      title: Estado
  mode: single
- id: '1695005076531'
  alias: Status - Window - Games - Closed
  description: ''
  trigger:
  - platform: state
    entity_id:
    - binary_sensor.juegos_sensor_ventana_contact
    to: 'off'
  condition: []
  action:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.ventana_juegos
  mode: single
- id: '1695005096958'
  alias: Status - Window - Games - Opened
  description: ''
  trigger:
  - platform: state
    entity_id:
    - binary_sensor.juegos_sensor_ventana_contact
    to: 'on'
  condition: []
  action:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.ventana_juegos
  mode: single
- id: '1695005190487'
  alias: Status - Window - Bedroom - Opened
  description: ''
  triggers:
  - type: opened
    device_id: 7a569bbc4a618b80ec9618d368dd1a79
    entity_id: 428520c4bb63b6d5bbc29f282af59d47
    domain: binary_sensor
    trigger: device
  conditions: []
  actions:
  - data: {}
    target:
      entity_id: input_boolean.ventana_habitacion_principal
    action: input_boolean.turn_off
  mode: single
- id: '1695005215339'
  alias: Status - Window - Bedroom - Closed
  description: ''
  triggers:
  - type: not_opened
    device_id: 7a569bbc4a618b80ec9618d368dd1a79
    entity_id: 428520c4bb63b6d5bbc29f282af59d47
    domain: binary_sensor
    trigger: device
  conditions: []
  actions:
  - data: {}
    target:
      entity_id: input_boolean.ventana_habitacion_principal
    action: input_boolean.turn_on
  mode: single
- id: '1695831304402'
  alias: Control - Presence - Lights - Kitchen - ON
  description: ''
  triggers:
  - type: occupied
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
    trigger: device
  conditions:
  - condition: time
    after: '17:30:00'
    before: 06:00:00
    enabled: true
  - condition: state
    entity_id: input_boolean.location_daniel_home
    state: 'on'
    enabled: true
  actions:
  - alias: Almita Guard Clause
    if:
    - condition: time
      after: '22:30:00'
      before: 06:00:00
    - condition: state
      entity_id: input_boolean.group_mode
      state: 'on'
    then:
    - stop: Almita is here
  - action: automation.trigger
    target:
      entity_id:
      - automation.control_arrive_leave_colorized
    data:
      skip_condition: true
  - alias: is not too late?
    if:
    - condition: time
      after: '18:00:00'
      before: '22:00:00'
    then:
    - data:
        brightness_pct: 100
        kelvin: 2000
      action: light.turn_on
      target:
        entity_id:
        - light.kitchen_light_oven
        - light.living_lamp
  mode: single
- id: 3ade403ec48b466198d0c76d971dc93a
  alias: Monitor - Network - Bad
  triggers:
  - value_template: '{{ states(''sensor.speedtest_download'') | float(100) < 100 }}'
    trigger: template
  actions:
  - action: notify.mobile_app_pixel_9_pro
    data:
      title: 'Internet '
      message: esta rarito
- id: '1696027483069'
  alias: Status - Leave / Arrive
  description: ''
  triggers:
  - entity_id:
    - input_boolean.puerta_principal
    to: 'off'
    trigger: state
    enabled: true
  conditions: []
  actions:
  - alias: Guard clause - Balcony Opened?
    if:
    - condition: state
      entity_id: input_boolean.ventana
      state: 'off'
    then:
    - stop: Balcony is open
  - alias: Si estoy saliendo
    if:
    - condition: state
      entity_id: input_boolean.location_daniel_home
      state: 'on'
    then:
    - wait_for_trigger:
      - entity_id:
        - input_boolean.puerta_principal
        to: 'on'
        trigger: state
      alias: Wait for the door to be closed
    - alias: Esperar ausencia
      wait_for_trigger:
      - entity_id:
        - binary_sensor.living_presence_occupancy
        to: 'off'
        trigger: state
      continue_on_timeout: false
      timeout:
        hours: 0
        minutes: 0
        seconds: 25
        milliseconds: 0
    - data: {}
      target:
        entity_id: input_boolean.location_daniel_home
      action: input_boolean.turn_off
    else:
    - data: {}
      target:
        entity_id: input_boolean.location_daniel_home
      action: input_boolean.turn_on
  mode: single
- id: '1696088648078'
  alias: Notify - Washer - Off
  description: ''
  triggers:
  - entity_id:
    - sensor.washer
    to: 'off'
    trigger: state
  conditions: []
  actions:
  - action: script.speak
    data:
      message: La lavadora ya terminó
  mode: single
- id: '1696172478241'
  alias: Control - Presence - Media - Play/Pause
  description: ''
  triggers:
  - type: motion
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
    for:
      hours: 0
      minutes: 0
      seconds: 0
    trigger: device
  conditions:
  - condition: state
    entity_id: remote.cuarto
    state: 'on'
  - condition: device
    device_id: d7e8029ab6b34ff6a352c21a557157c5
    domain: vacuum
    entity_id: 0f6c7891f3b129ee57c4ddc0f780d81e
    type: is_docked
    enabled: true
  - condition: state
    entity_id: input_boolean.group_mode
    state: 'off'
  actions:
  - data: {}
    target:
      entity_id: media_player.cuarto_2
    action: media_player.media_pause
  - wait_for_trigger:
    - type: no_motion
      device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
      entity_id: 1a2320a50b807697de9c4580527fc8cf
      domain: binary_sensor
      trigger: device
  - data: {}
    target:
      entity_id: media_player.cuarto_2
    action: media_player.media_play
  mode: single
- id: '1696462888881'
  alias: Control - Sound 5.1 - ON OFF
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.sonido_5_1
    to: 'on'
  - platform: state
    entity_id:
    - input_boolean.sonido_5_1
    to: 'off'
  condition: []
  action:
  - service: script.play_sound_power
    data: {}
  mode: single
- id: ba17208aeefa4d409ab478dbc6eee8db
  alias: Control - Juegos - TV - OFF
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'off'
    from: 'on'
  condition: []
  action:
  - if:
    - condition: device
      device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
      domain: media_player
      entity_id: 6584a1d94dfbaa77556863b2668b3d52
      type: is_on
    then:
    - type: turn_off
      device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
      entity_id: 48261ad70ba41bb57f5df8c570365660
      domain: remote
  mode: single
- id: fa218c298824443ab75976e9db0da39a
  alias: Control - Juegos - TV - ON
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'on'
    from: 'off'
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'on'
    from: unavailable
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'on'
    from: unknown
  condition: []
  action:
  - if:
    - condition: device
      device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
      domain: media_player
      entity_id: 6584a1d94dfbaa77556863b2668b3d52
      type: is_off
    then:
    - service: wake_on_lan.send_magic_packet
      data:
        mac: 80:8A:BD:2A:4E:7A
  mode: single
- id: '1696967967979'
  alias: Control - Work - On
  description: ''
  triggers:
  - entity_id:
    - input_boolean.trabajo_switch
    to: 'on'
    trigger: state
  conditions: []
  actions:
  - data: {}
    target:
      device_id: 279c35e8a163ec78d22e06f70d575901
    action: switch.turn_on
  - if:
    - condition: numeric_state
      entity_id: sensor.forecast_temperature
      above: 20
    then:
    - metadata: {}
      data: {}
      action: fan.turn_on
      target:
        entity_id: fan.studio_fan
  - action: script.blackouts_up
    metadata: {}
    data: {}
  mode: single
- id: '1696968028826'
  alias: Control - Work - Off
  description: ''
  triggers:
  - entity_id:
    - input_boolean.trabajo_switch
    to: 'off'
    trigger: state
  conditions: []
  actions:
  - parallel:
    - action: shell_command.turn_off_remote_mac
      data: {}
    - action: switch.turn_off
      target:
        device_id:
        - 171ec471cb16397805c82c62493f91fa
      data: {}
    - metadata: {}
      data: {}
      target:
        entity_id: fan.studio_fan
      enabled: true
      action: fan.turn_off
    - action: light.turn_off
      metadata: {}
      data: {}
      target:
        entity_id: light.studio_light_l1
    - action: script.speak
      metadata: {}
      data:
        message: Cambiando de actividad
      enabled: false
    - data: {}
      target:
        device_id:
        - 279c35e8a163ec78d22e06f70d575901
      action: switch.turn_off
  - delay:
      hours: 0
      minutes: 0
      seconds: 20
      milliseconds: 0
    enabled: false
  mode: single
- id: '1697056900612'
  alias: 'Notify - Security - Motion '
  description: ''
  triggers:
  - type: opened
    device_id: 56fc9ca07768efa6368988928dcaeeb4
    entity_id: 598f0e8dd69d801036265e917d2b8f6b
    domain: binary_sensor
    trigger: device
  - type: opened
    device_id: d1ab74b56977fe5714a18bfec56ed865
    entity_id: e6595bc28cc35bf5ef3b121b6830b20b
    domain: binary_sensor
    trigger: device
  - type: opened
    device_id: 37411987c5d0b69b22eb7db9fa66ffa0
    entity_id: ec6632fe5f125e3d8e2cfcf50d3da82f
    domain: binary_sensor
    trigger: device
  - type: occupied
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
    trigger: device
  conditions:
  - condition: state
    entity_id: input_boolean.location_daniel_home
    state: 'off'
  actions:
  - action: script.send_push_notification
    data:
      message: Alguien caminando en la casa
      title: Alerta Intruso
  mode: restart
- id: '1697139932709'
  alias: Control - Light - Bombillo - Barra - Camara
  description: ''
  trigger:
  - platform: time_pattern
    seconds: /1
  condition: []
  action:
  - service: light.turn_on
    metadata: {}
    data: {}
    target:
      entity_id:
      - light.living_strip
      - light.kitchen_led_strip
      - light.kitchen_light_oven
  - service: color_extractor.turn_on
    data:
      color_extract_path: /config/www/camera.jpg
      brightness_pct: 100
      transition: 1
    target:
      entity_id:
      - light.living_strip
  - service: color_extractor.turn_on
    data:
      color_extract_path: /config/www/camera.jpg
      brightness_pct: 100
      transition: 1
    target:
      entity_id:
      - light.kitchen_light_oven
  - service: color_extractor.turn_on
    data:
      color_extract_path: /config/www/camera.jpg
      brightness_pct: 100
      transition: 1
    target:
      entity_id:
      - light.kitchen_led_strip
  mode: single
- id: '1697291120507'
  alias: Control - Charger - TV
  description: ''
  triggers:
  - device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
    domain: media_player
    entity_id: 6584a1d94dfbaa77556863b2668b3d52
    type: turned_on
    trigger: device
  conditions:
  - condition: device
    device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
    domain: media_player
    entity_id: 6584a1d94dfbaa77556863b2668b3d52
    type: is_on
  - condition: device
    type: is_off
    device_id: 3737e65e7235891fd6579ac17e7258d2
    entity_id: c383282c2a14eed3860af41a9df3f61f
    domain: switch
  actions:
  - data: {}
    target:
      entity_id: switch.charger
    action: switch.turn_on
  - action: script.speak
    data:
      message: Pon a cargar el celular
  mode: single
- id: '1697291227029'
  alias: Status - TV - input boolean sync - OFF
  description: ''
  trigger:
  - platform: device
    device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
    domain: media_player
    entity_id: 6584a1d94dfbaa77556863b2668b3d52
    type: turned_off
    for:
      hours: 0
      minutes: 0
      seconds: 0
  condition: []
  action:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.juegos_tv
  mode: single
- id: '1697552722120'
  alias: Status - TV - input boolean sync - ON
  description: ''
  trigger:
  - platform: device
    device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
    domain: media_player
    entity_id: 6584a1d94dfbaa77556863b2668b3d52
    type: turned_on
  - platform: device
    device_id: b52e5d8af655c0d1ace2836b2ab7a5cf
    domain: media_player
    entity_id: f3a1fdc54908cf9b443183a3ba3edd85
    type: turned_on
  condition: []
  action:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.juegos_tv
  mode: single
- id: '1697552799302'
  alias: Control - Movie Mode - TV - ON
  description: ''
  triggers:
  - entity_id:
    - input_boolean.juegos_tv
    to: 'on'
    trigger: state
  conditions: []
  actions:
  - parallel:
    - alias: Blackout
      if:
      - condition: time
        after: 05:00:00
        before: '17:30:00'
      then:
      - action: script.play_blackout_down
        data: {}
      else:
      - data: {}
        action: script.play_roller_middle
    - data: {}
      target:
        area_id:
        - balcony
        - kitchen
        - bedroom
        - main_bathroom
        - living_room
        - studio
      action: light.turn_off
    - type: toggle
      device_id: 3146447acf58c7bb7135db2669230632
      entity_id: 9579cb6f3d8802806fc8ade2755c7317
      domain: switch
    - metadata: {}
      data: {}
      target:
        entity_id: input_boolean.sonido_5_1
      action: input_boolean.turn_on
    - if:
      - condition: numeric_state
        entity_id: sensor.forecast_temperature
        above: 23
      then:
      - alias: Fan
        if:
        - condition: numeric_state
          entity_id: weather.forecast_home_2
          attribute: temperature
          above: 15
        then:
        - type: turn_on
          device_id: 558c9a17ab1ba780e0c269d5257b653c
          entity_id: 904147692121cd0d2be4b8c0b5267448
          domain: fan
        - metadata: {}
          data:
            percentage: 15
          target:
            entity_id: fan.games_fan
          action: fan.set_percentage
        enabled: true
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: light.games_lamp
  mode: single
- id: '1697552826198'
  alias: Control - Movie Mode - TV - OFF
  description: ''
  triggers:
  - entity_id:
    - input_boolean.juegos_tv
    to: 'off'
    trigger: state
  conditions: []
  actions:
  - parallel:
    - type: turn_off
      device_id: 558c9a17ab1ba780e0c269d5257b653c
      entity_id: 904147692121cd0d2be4b8c0b5267448
      domain: fan
    - data: {}
      action: script.play_blackout_up_safe
    - metadata: {}
      data: {}
      target:
        entity_id: input_boolean.sonido_5_1
      action: input_boolean.turn_off
    - metadata: {}
      data: {}
      action: light.turn_off
      target:
        area_id: play_room
    - type: toggle
      device_id: 0c6d3b0e4a84d8382c004c18aa3cccca
      entity_id: 109e30d051aa3c7ab426c7afe01f4814
      domain: switch
  mode: single
- id: '1697675154658'
  alias: Control - Boton - Cocina
  description: ''
  trigger:
  - platform: device
    domain: mqtt
    device_id: f4071459ebca942cecdcc05cb7aa2de1
    type: action
    subtype: single
    discovery_id: 0xa4c13837c7357ff6 action_single
  condition: []
  action:
  - service: light.toggle
    data: {}
    target:
      entity_id: light.kitchen_light_left
  mode: single
- id: '1697675209780'
  alias: Control - Button - Kitchen - 3
  description: ''
  triggers:
  - domain: mqtt
    device_id: f4071459ebca942cecdcc05cb7aa2de1
    type: action
    subtype: hold
    discovery_id: 0xa4c13837c7357ff6 action_hold
    trigger: device
  conditions: []
  actions:
  - action: script.control_pomodoro_on_or_cancel
    data: {}
  mode: single
- id: '1697676676801'
  alias: Control - Button - Kitchen - 2
  description: ''
  triggers:
  - domain: mqtt
    device_id: f4071459ebca942cecdcc05cb7aa2de1
    type: action
    subtype: double
    trigger: device
  conditions: []
  actions:
  - action: switch.toggle
    metadata: {}
    data: {}
    target:
      entity_id: switch.regleta_l3
  - if:
    - condition: sun
      before: sunrise
      after: sunset
    then:
    - data: {}
      target:
        area_id: kitchen
      action: light.turn_off
    - data: {}
      target:
        entity_id: light.kitchen_light_center
      action: light.toggle
  mode: single
- id: '1697677962529'
  alias: Control - Games - Button - Sofa Up
  description: ''
  triggers:
  - domain: mqtt
    device_id: d7f1336654c71014681469d5d8145132
    type: action
    subtype: button_1_double
    trigger: device
  conditions: []
  actions:
  - metadata: {}
    data: {}
    target:
      entity_id: switch.sofa_up
    action: switch.toggle
  mode: single
- id: '1697678021409'
  alias: Control - Games - Button - Sofa Down
  description: ''
  triggers:
  - domain: mqtt
    device_id: d7f1336654c71014681469d5d8145132
    type: action
    subtype: button_1_single
    trigger: device
  conditions: []
  actions:
  - action: light.toggle
    metadata: {}
    data: {}
    target:
      entity_id: light.living_light_left
  mode: single
- id: '1697680409972'
  alias: Control - Living - Button - Double
  description: ''
  triggers:
  - domain: mqtt
    device_id: 5f28aa0e2da5b49b4ae58a76f40e5970
    type: action
    subtype: double
    discovery_id: 0xa4c138c3dd516416 action_double
    trigger: device
  conditions: []
  actions:
  - if:
    - condition: state
      entity_id: input_boolean.is_pomodoro_running
      state: 'on'
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: Leer - Profesional - 1 Pomodoro
  - action: script.control_pomodoro_on_or_cancel
    data: {}
  mode: single
- id: '1697751498283'
  alias: Home - Jaimito Robot - Safe Guard after ON
  description: ''
  triggers:
  - entity_id:
    - vacuum.robot
    to: cleaning
    trigger: state
  conditions: []
  actions:
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - if:
    - condition: state
      entity_id: input_boolean.ventana
      state: 'off'
      enabled: false
    - type: is_open
      condition: device
      device_id: d1ab74b56977fe5714a18bfec56ed865
      entity_id: e6595bc28cc35bf5ef3b121b6830b20b
      domain: binary_sensor
    then:
    - data: {}
      action: script.robot_go_back_to_base
  mode: restart
- id: '1697841094516'
  alias: Control - Studio - Lights
  description: ''
  triggers:
  - entity_id:
    - sensor.living_presence_illuminance
    below: 400
    for:
      hours: 0
      minutes: 10
      seconds: 0
    id: darker
    trigger: numeric_state
  - entity_id:
    - sensor.living_presence_illuminance
    for:
      hours: 0
      minutes: 30
      seconds: 0
    above: 450
    id: lighter
    trigger: numeric_state
  conditions:
  - condition: state
    entity_id: input_boolean.trabajo_switch
    state: 'on'
  - condition: device
    type: is_off
    device_id: 062270584ebb174c53d3524a068565c8
    entity_id: c6025369c0b73228653bec8873b027a5
    domain: light
  actions:
  - if:
    - condition: trigger
      id:
      - lighter
    then:
    - metadata: {}
      data: {}
      target:
        entity_id: light.studio_light_l1
      action: light.turn_off
    else:
    - data: {}
      action: light.turn_on
      target:
        entity_id: light.studio_light_l1
  mode: single
- id: '1698988501568'
  alias: Control - Presence - Lights - Kitchen - OFF
  description: ''
  triggers:
  - type: not_occupied
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
    trigger: device
  conditions:
  - condition: state
    entity_id: input_boolean.is_pomodoro_running
    state: 'off'
  - condition: numeric_state
    entity_id: sensor.living_presence_target_distance
    above: 599
    enabled: false
  actions:
  - data: {}
    target:
      area_id:
      - kitchen
      - living_room
      label_id: light_colored
    action: light.turn_off
  - if:
    - condition: state
      entity_id: input_boolean.trabajo_switch
      state: 'off'
    then:
    - metadata: {}
      data: {}
      target:
        area_id: studio
      action: light.turn_off
  mode: single
- id: '1699028320199'
  alias: Control - Studio - Button - Double
  description: ''
  triggers:
  - domain: mqtt
    device_id: f21f81433a3a085fb96220b7919dcaf2
    type: action
    subtype: button_1_double
    trigger: device
  conditions: []
  actions:
  - action: input_boolean.toggle
    target:
      entity_id: input_boolean.trabajo_switch
    data: {}
  mode: single
- id: '1700068861966'
  alias: Notify - Location - Windows Open
  description: ''
  triggers:
  - entity_id:
    - input_boolean.location_daniel_home
    to: 'off'
    trigger: state
  conditions:
  - condition: or
    conditions:
    - condition: state
      entity_id: input_boolean.ventana
      state: 'off'
    - condition: state
      entity_id: input_boolean.ventana_habitacion_principal
      state: 'off'
    - condition: state
      entity_id: input_boolean.ventana_juegos
      state: 'off'
    - condition: state
      entity_id: input_boolean.ventana_ropas
      state: 'off'
  actions:
  - action: script.send_push_notification
    data:
      message: Tienes las ventanas abiertas
      title: ALERTA
  mode: single
- id: '1702739237783'
  alias: Control - Cafetera - Off - Auto
  description: ''
  trigger:
  - platform: state
    entity_id:
    - switch.power_strip_switch_coffee_maker
    to: 'on'
  condition: []
  action:
  - service: switch.turn_on
    target:
      entity_id: switch.power_strip_switch_5
    data: {}
  - delay:
      hours: 0
      minutes: 20
      seconds: 0
      milliseconds: 0
  - service: tts.google_translate_say
    data:
      cache: false
      language: es
      entity_id: media_player.casa_2
      message: Dani, apagué la cafetera para que no explote
  - service: switch.turn_off
    target:
      entity_id: switch.power_strip_switch_coffee_maker
    data: {}
  - service: switch.turn_off
    target:
      entity_id:
      - switch.power_strip_switch_5
      device_id: []
      area_id: []
    data: {}
  mode: single
- id: '1705156232730'
  alias: Control - Bombillo - Habitacion - Fingerbot - On
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.habitacion_bombilo_input
    to: 'on'
  condition: []
  action:
  - service: script.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: script.fingerbot_giro_right
  mode: single
- id: '1705156253951'
  alias: Control - Bombillo - Habitacion - Fingerbot - Off
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.habitacion_bombilo_input
    to: 'off'
  condition: []
  action:
  - service: script.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: script.fingerbot_giro_left
  mode: single
- id: '1705167199758'
  alias: Notify - ON Devices - Went OFF - Switch
  description: ''
  triggers:
  - type: turned_off
    device_id: 46e143d73006a31b9ba79db254a4a205
    entity_id: 583d1cf6aaaa2956b64e7dddead8db94
    domain: switch
    id: freezer
    trigger: device
  - type: turned_off
    device_id: 1aa6f98bd2292b131cd849e6c879efd3
    entity_id: b0a8e1247217c8655cf6a1525e1f7a7a
    domain: switch
    id: games
    trigger: device
  - type: turned_off
    device_id: 092d4358f9e11f2d3ce3ed0e81d5891e
    entity_id: 8c6e47dd9cf38d50aa2198ea5db5df8d
    domain: switch
    trigger: device
    id: HA
  conditions: []
  actions:
  - if:
    - condition: trigger
      id:
      - freezer
    then:
    - action: switch.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: switch.kitchen_freezer_switch
    - action: script.speak
      data:
        message: Cuidado! la nevera está apagada
    - device_id: a764c034127008c652d1681065cd7460
      domain: mobile_app
      type: notify
      title: Status
      message: 'El Freezer se apagó pero ya volvió '
  - if:
    - condition: trigger
      id:
      - games
    then:
    - action: switch.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: switch.studio_server_switch
    - action: script.speak
      data:
        message: 'Cuidado! El servidor se apagó '
    - device_id: a764c034127008c652d1681065cd7460
      domain: mobile_app
      type: notify
      title: Status
      message: 'Los juegos de apagaron '
  - if:
    - condition: trigger
      id:
      - HA
    then:
    - action: switch.turn_on
      metadata: {}
      data: {}
      target:
        entity_id:
        - switch.juegos_switch
    - action: script.speak
      data:
        message: Cuidado! los juegos están apagados
    - device_id: a764c034127008c652d1681065cd7460
      domain: mobile_app
      type: notify
      title: Status
      message: 'El home Assistant se apagó '
  mode: single
- id: '1705415950357'
  alias: Notify - Low Battery
  description: ''
  use_blueprint:
    path: sbyx/low-battery-level-detection-notification-for-all-battery-sensors.yaml
    input:
      threshold: 15
      actions:
      - action: script.send_push_notification
        data:
          message: '{{sensors|replace("_"," ")}} esta descargado'
          title: Bateria baja
      day: 1
      time: 09:00:00
- id: '1705777560451'
  alias: Control - Temperature Fan - Night - Bedroom - Rest
  description: ''
  triggers:
  - hours: '23'
    trigger: time_pattern
  - hours: '0'
    trigger: time_pattern
  - hours: '1'
    trigger: time_pattern
  - hours: '2'
    trigger: time_pattern
  - hours: '3'
    trigger: time_pattern
  - hours: '4'
    trigger: time_pattern
  - hours: '5'
    trigger: time_pattern
  conditions:
  - condition: state
    entity_id: input_boolean.location_daniel_home
    state: 'on'
  - condition: state
    entity_id: input_boolean.trabajo_switch
    state: 'off'
  - condition: state
    entity_id: input_boolean.juegos_tv
    state: 'off'
  actions:
  - if:
    - condition: state
      entity_id: input_boolean.mode_party
      state: 'off'
    then:
    - if:
      - condition: numeric_state
        entity_id: sensor.forecast_temperature
        below: 20
      then:
      - stop: It's too cold
    enabled: false
  - action: fan.turn_on
    target:
      entity_id: fan.bedroom_fan
    data:
      preset_mode: sleep
  - action: fan.set_percentage
    target:
      entity_id:
      - fan.bedroom_fan
    data:
      percentage: 1
  - action: fan.set_preset_mode
    target:
      entity_id:
      - fan.bedroom_fan
    data:
      preset_mode: sleep
  - action: fan.oscillate
    target:
      entity_id:
      - fan.bedroom_fan
    data:
      oscillating: true
  - if:
    - condition: state
      entity_id: input_boolean.mode_party
      state: 'off'
    then:
    - delay:
        hours: 0
        minutes: 20
        seconds: 0
        milliseconds: 0
    else:
    - delay:
        hours: 0
        minutes: 40
        seconds: 0
        milliseconds: 0
  - action: fan.turn_off
    target:
      entity_id: fan.bedroom_fan
    data: {}
  mode: queued
  max: 5
- id: '1709304891435'
  alias: Control - Games - Button - Sofa Up 2
  description: ''
  triggers:
  - domain: mqtt
    device_id: d7f1336654c71014681469d5d8145132
    type: action
    subtype: button_2_single
    trigger: device
  conditions: []
  actions:
  - metadata: {}
    data: {}
    target:
      entity_id: switch.sofa_down_2
    action: switch.toggle
  mode: single
- id: '1709305044135'
  alias: Control - Games - Button - Fan
  description: ''
  triggers:
  - domain: mqtt
    device_id: d7f1336654c71014681469d5d8145132
    type: action
    subtype: button_2_double
    trigger: device
  conditions: []
  actions:
  - action: fan.toggle
    target:
      entity_id: fan.games_fan
    data: {}
  mode: single
- id: '1711296509884'
  alias: Status - Location - Living Room - Is Ocuped?
  description: ''
  trigger:
  - type: motion
    platform: device
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
  - type: no_motion
    platform: device
    device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
    entity_id: 1a2320a50b807697de9c4580527fc8cf
    domain: binary_sensor
  condition: []
  action:
  - if:
    - type: is_motion
      condition: device
      device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
      entity_id: 1a2320a50b807697de9c4580527fc8cf
      domain: binary_sensor
    then:
    - service: input_boolean.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: input_boolean.location_living_room_is_ocuped
    else:
    - service: input_boolean.turn_off
      target:
        entity_id:
        - input_boolean.location_living_room_is_ocuped
      data: {}
  mode: single
- id: '1711932789452'
  alias: Control - Clothes - Light
  description: ''
  triggers:
  - type: opened
    device_id: 56fc9ca07768efa6368988928dcaeeb4
    entity_id: 598f0e8dd69d801036265e917d2b8f6b
    domain: binary_sensor
    trigger: device
  - type: not_opened
    device_id: 56fc9ca07768efa6368988928dcaeeb4
    entity_id: 598f0e8dd69d801036265e917d2b8f6b
    domain: binary_sensor
    trigger: device
  conditions:
  - condition: time
    after: '18:00:00'
    before: 06:00:00
  actions:
  - if:
    - type: is_open
      condition: device
      device_id: 56fc9ca07768efa6368988928dcaeeb4
      entity_id: 598f0e8dd69d801036265e917d2b8f6b
      domain: binary_sensor
    then:
    - metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_right
      action: light.turn_on
    else:
    - metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_right
      action: light.turn_off
  mode: restart
- id: '1712098733732'
  alias: Control - Bathroom Light - ON
  description: ''
  triggers:
  - type: opened
    device_id: d1ab74b56977fe5714a18bfec56ed865
    entity_id: e6595bc28cc35bf5ef3b121b6830b20b
    domain: binary_sensor
    trigger: device
  conditions:
  - condition: time
    after: '18:00:00'
    before: 06:00:00
    enabled: false
  actions:
  - metadata: {}
    data: {}
    target:
      entity_id: timer.bathroom_use
    action: timer.start
  - action: light.turn_on
    metadata: {}
    data:
      kelvin: 2436
    target:
      device_id: b66b33fcdb579e9c2f56acdd68449eb0
  mode: single
- id: '1712266717475'
  alias: Control - Balcony - Button - First
  description: ''
  trigger:
  - platform: device
    domain: mqtt
    device_id: d4b3b039d439642e1882f643aabfee4f
    type: action
    subtype: single
  condition: []
  action:
  - service: light.toggle
    metadata: {}
    data: {}
    target:
      entity_id: light.balcon_bombillo
  mode: single
- id: '1712266746658'
  alias: Control - Balcony - Button - Second
  description: ''
  trigger:
  - platform: device
    domain: mqtt
    device_id: d4b3b039d439642e1882f643aabfee4f
    type: action
    subtype: double
  condition: []
  action:
  - service: light.toggle
    metadata: {}
    data: {}
    target:
      entity_id: light.balcony_light_corner
  mode: single
- id: '1712266766525'
  alias: Control - Balcony - Button - hold
  description: ''
  trigger:
  - platform: device
    domain: mqtt
    device_id: d4b3b039d439642e1882f643aabfee4f
    type: action
    subtype: hold
  condition: []
  action:
  - service: script.mode_pomodoro_on
    metadata: {}
    data: {}
  mode: single
- id: '1712596447274'
  alias: Notify - Home Assistant was turned on
  description: ''
  triggers:
  - event: start
    trigger: homeassistant
  conditions: []
  actions:
  - action: script.send_push_notification
    data:
      message: Se prendió el Home Assistant
      title: Power Alert
  - action: light.turn_off
    metadata: {}
    data: {}
    target:
      area_id:
      - kitchen
      - balcony
  - delay:
      hours: 0
      minutes: 1
      seconds: 0
      milliseconds: 0
  - action: switch.turn_off
    metadata: {}
    data: {}
    target:
      entity_id: switch.games_rf_switch
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - action: switch.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: switch.games_rf_switch
  mode: single
- id: '1712965466432'
  alias: Control - Arrive - Blink
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.location_daniel_home
    to: 'on'
  condition:
  - condition: time
    after: '18:00:00'
    before: 06:00:00
  action:
  - service: script.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: script.blink_light_home
  mode: single
- id: '1712966450342'
  alias: Control - Door Opened - Blink
  description: ''
  trigger:
  - type: opened
    platform: device
    device_id: 9659289c13a7b4cd00daa3f183d182dc
    entity_id: 26c83c28515daaf69dd8eb850ca2b4e5
    domain: binary_sensor
  condition: []
  action:
  - service: script.blink_light_home
    metadata: {}
    data: {}
  mode: single
- id: '1713026588278'
  alias: Control - Bathroom Light - OFF
  description: Turn off the bathroom light based on door state and timing.
  triggers:
  - entity_id:
    - binary_sensor.bathroom_door_contact
    from: 'on'
    to: 'off'
    trigger: state
  actions:
  - if:
    - condition: state
      entity_id: timer.bathroom_use
      state: idle
    then:
    - type: turn_off
      device_id: b66b33fcdb579e9c2f56acdd68449eb0
      entity_id: e89c0e79f312f4133521e3cb63a7aa87
      domain: light
    else:
    - stop: I am using the bathroom
  mode: restart
- id: '1713569541932'
  alias: Control - Games Light - TV
  description: ''
  trigger:
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'on'
  - platform: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'off'
  condition:
  - condition: time
    after: '18:00:00'
    before: 06:00:00
  action:
  - if:
    - condition: trigger
      id:
      - 'on'
      - idle
    then:
    - service: light.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: light.games_lamp_light
    - delay:
        hours: 0
        minutes: 0
        seconds: 10
        milliseconds: 0
    - service: light.turn_off
      data: {}
      target:
        entity_id: light.games_lamp_light
  - if:
    - condition: trigger
      id:
      - 'off'
      - idle
    then:
    - service: light.turn_off
      data: {}
      target:
        entity_id: light.games_lamp_light
  mode: single
- id: '1713706439994'
  alias: Control - Studio - Button
  description: ''
  triggers:
  - domain: mqtt
    device_id: f21f81433a3a085fb96220b7919dcaf2
    type: action
    subtype: button_1_single
    trigger: device
  conditions: []
  actions:
  - if:
    - condition: state
      entity_id: input_boolean.is_pomodoro_running
      state: 'on'
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: Trabajar
  - action: script.control_pomodoro_on_or_cancel
    data: {}
  mode: single
- id: '1713706546621'
  alias: 'Control - Studio - Button - 2 '
  description: ''
  triggers:
  - domain: mqtt
    device_id: f21f81433a3a085fb96220b7919dcaf2
    type: action
    subtype: button_2_single
    trigger: device
  conditions: []
  actions:
  - action: light.toggle
    target:
      entity_id:
      - light.studio_light_l1
    data: {}
  mode: single
- id: '1714152222860'
  alias: Control - Z2M MQTT - Restart
  description: ''
  trigger:
  - platform: state
    entity_id:
    - binary_sensor.zigbee2mqtt_bridge_connection_state
    to: 'off'
  condition: []
  action:
  - action: script.send_push_notification
    data:
      message: Z2M reiniciado
      title: Status
  - service: hassio.addon_stop
    data:
      addon: core_mosquitto
  - service: hassio.addon_stop
    data:
      addon: 45df7312_zigbee2mqtt
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - service: hassio.addon_start
    data:
      addon: core_mosquitto
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - service: hassio.addon_start
    data:
      addon: 45df7312_zigbee2mqtt
  mode: single
- id: '1714168639795'
  alias: Control - Bathroom - Shower Mode - ON
  description: ''
  trigger:
  - type: not_opened
    platform: device
    device_id: 55c8d5cb3c315726d8ed170fb02039ca
    entity_id: 78cae655efed33ed617b7c8f228bd154
    domain: binary_sensor
  condition: []
  action:
  - service: script.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: script.shower_mode_on
  mode: restart
- id: '1714180761353'
  alias: Control - Timer - Bathroom Time Alarm Countdown
  description: ''
  triggers:
  - entity_id:
    - timer.shower_timer
    to: active
    id: active
    trigger: state
  - event_type: timer.cancelled
    event_data:
      entity_id: timer.shower_timer
    id: cancel
    trigger: event
  conditions: []
  actions:
  - alias: If canceled stop
    if:
    - condition: trigger
      id:
      - cancel
    then:
    - metadata: {}
      data: {}
      action: script.mode_shower_off
    - stop: It was canceled
    else:
    - wait_for_trigger:
      - entity_id:
        - timer.shower_timer
        to: idle
        trigger: state
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        device_id: b66b33fcdb579e9c2f56acdd68449eb0
  mode: restart
- id: '1714407184408'
  alias: Status - Leave / Arrive - android auto
  description: ''
  triggers:
  - type: connected
    device_id: a764c034127008c652d1681065cd7460
    entity_id: 17611f2c28e9188e5e7934e93b89137f
    domain: binary_sensor
    trigger: device
  - type: not_connected
    device_id: a764c034127008c652d1681065cd7460
    entity_id: 17611f2c28e9188e5e7934e93b89137f
    domain: binary_sensor
    trigger: device
  conditions: []
  actions:
  - metadata: {}
    data: {}
    target:
      entity_id: input_boolean.location_daniel_home
    action: input_boolean.turn_off
  mode: single
- alias: Control - TV - WakeOnLan
  trigger:
  - platform: device
    device_id: 1e8d7c99f068a69f13ec0fca5cbf00d6
    domain: media_player
    entity_id: 6584a1d94dfbaa77556863b2668b3d52
    type: turned_on
  action:
  - service: wake_on_lan.send_magic_packet
    data:
      mac: 80:8A:BD:2A:4E:7A
  id: 020ab81584a846c3a26817fa393d265e
- id: '1716261483087'
  alias: Control - charger off
  description: ''
  triggers:
  - entity_id:
    - sensor.pixel_9_pro_battery_level
    to: '100'
    trigger: state
  conditions:
  - condition: state
    entity_id: input_boolean.location_daniel_home
    state: 'on'
  - condition: or
    conditions:
    - condition: device
      type: is_on
      device_id: 3737e65e7235891fd6579ac17e7258d2
      entity_id: c383282c2a14eed3860af41a9df3f61f
      domain: switch
    - condition: state
      entity_id: switch.regleta_l2
      state: 'on'
  actions:
  - type: turn_off
    device_id: 3737e65e7235891fd6579ac17e7258d2
    entity_id: c383282c2a14eed3860af41a9df3f61f
    domain: switch
  - metadata: {}
    data: {}
    target:
      entity_id: switch.regleta_l2
    action: switch.turn_off
  mode: single
- id: '1716395575154'
  alias: Control - Presence - Location - Arrive - Colorized
  description: ''
  triggers:
  - entity_id:
    - input_boolean.location_daniel_home
    to: 'on'
    trigger: state
  conditions:
  - condition: time
    after: '18:00:00'
    before: 06:00:00
  actions:
  - data:
      effect: colorloop
      brightness: 255
    target:
      entity_id:
      - light.living_neon_small
      - light.living_neon
    action: light.turn_on
  - delay:
      hours: 0
      minutes: 0
      seconds: 1
      milliseconds: 0
  - data:
      effect: colorloop
    target:
      entity_id:
      - light.kitchen_led_strip
      - light.kitchen_freezer_strip
    action: light.turn_on
  - if:
    - condition: time
      after: '17:30:00'
      before: '12:00:00'
    then: []
  mode: single
- id: '1729105219763'
  alias: Control - Studio - Button - 2 Double
  description: ''
  triggers:
  - domain: mqtt
    device_id: f21f81433a3a085fb96220b7919dcaf2
    type: action
    subtype: button_2_double
    trigger: device
  conditions: []
  actions:
  - action: fan.toggle
    data: {}
    target:
      entity_id: fan.studio_fan
  mode: single
- id: '1729341480197'
  alias: Control - Living - Button - Single
  description: ''
  triggers:
  - domain: mqtt
    device_id: 5f28aa0e2da5b49b4ae58a76f40e5970
    type: action
    subtype: single
    trigger: device
  conditions: []
  actions:
  - action: light.toggle
    target:
      entity_id: light.living_light_left
    data: {}
  mode: single
- id: '1729342124920'
  alias: Control - Living - Button - Hold
  description: ''
  triggers:
  - domain: mqtt
    device_id: 5f28aa0e2da5b49b4ae58a76f40e5970
    type: action
    subtype: hold
    trigger: device
  conditions: []
  actions:
  - action: light.turn_off
    target:
      area_id:
      - balcony
      - main_bathroom
      - clothes_zone
      - play_room
      - bedroom
      - living_room
      - kitchen
      - studio
    data: {}
  mode: single
- id: '1729641599847'
  alias: Presence - Games - Lights - ON/OFF
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - binary_sensor.games_presence_occupancy
    id: ENTER
    to: 'on'
  - trigger: state
    entity_id:
    - binary_sensor.games_presence_occupancy
    id: LEAVE
    to: away
  conditions: []
  actions:
  - if:
    - condition: sun
      after: sunrise
      before: sunset
    - condition: trigger
      id:
      - ENTER
    - condition: device
      type: is_on
      device_id: b52e5d8af655c0d1ace2836b2ab7a5cf
      entity_id: 689eea69bd5d08bdf593910a0d8e4565
      domain: remote
    then:
    - stop: Its in the day
    enabled: false
  - if:
    - condition: trigger
      id:
      - ENTER
    then:
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        area_id: play_room
    - delay:
        hours: 0
        minutes: 0
        seconds: 20
    - action: light.turn_off
      target:
        entity_id: light.games_light
      data: {}
    - if:
      - type: is_battery_level
        condition: device
        device_id: a764c034127008c652d1681065cd7460
        entity_id: 50d1b7a868fd9888ef55e2fb92c42388
        domain: sensor
        below: 60
      - condition: not
        conditions:
        - condition: state
          entity_id: sensor.pixel_9_pro_battery_state
          state: charging
      then:
      - type: turn_on
        device_id: 3737e65e7235891fd6579ac17e7258d2
        entity_id: c383282c2a14eed3860af41a9df3f61f
        domain: switch
      - action: script.speak
        metadata: {}
        data:
          message: Pon a cargar el celular
    else:
    - action: light.turn_off
      metadata: {}
      data: {}
      target:
        area_id: play_room
  mode: restart
- id: '1730107418859'
  alias: Kitchen - Lights - Energy Saver
  description: ''
  triggers:
  - type: changed_states
    device_id: a8de8719dbf7ac3444215bb03fea7643
    entity_id: de352feb226c1d56d927eb72d888ed89
    domain: light
    trigger: device
    id: LIVING
  - type: changed_states
    device_id: 470321e816867853742830b815c5e937
    entity_id: 46a1523f0098104171dc7dadf273fd78
    domain: switch
    trigger: device
    id: KITCHEN
  conditions:
  - condition: time
    after: '17:30:00'
    before: 06:00:00
  - condition: state
    entity_id: input_boolean.mode_party
    state: 'off'
  - condition: numeric_state
    entity_id: sensor.living_presence_target_distance
    below: 580
  actions:
  - if:
    - condition: trigger
      id:
      - LIVING
    then:
    - if:
      - condition: state
        entity_id: light.living_light_left
        state: 'on'
      then:
      - action: light.turn_off
        metadata: {}
        data: {}
        target:
          entity_id:
          - light.kitchen_led_strip
          - light.kitchen_freezer_strip
          - light.0xa4c138e94e11c12f
          - light.humidifier
          - light.living_neon_small
      else:
      - action: automation.trigger
        metadata: {}
        data:
          skip_condition: true
        target:
          entity_id: automation.control_lights_on
  - if:
    - condition: trigger
      id:
      - KITCHEN
    then:
    - if:
      - condition: state
        entity_id: light.kitchen_freezer_strip
        state: 'on'
      then:
      - action: light.turn_off
        metadata: {}
        data: {}
        target:
          entity_id:
          - light.kitchen_led_strip
          - light.kitchen_freezer_strip
          - light.0xa4c138e94e11c12f
          - light.humidifier
          - light.living_neon_small
      else:
      - action: automation.trigger
        metadata: {}
        data:
          skip_condition: true
        target:
          entity_id: automation.control_lights_on
  mode: single
- id: '1730211288647'
  alias: Control - Robot - Discharge - Return
  description: ''
  triggers:
  - device_id: d7e8029ab6b34ff6a352c21a557157c5
    domain: vacuum
    entity_id: 0f6c7891f3b129ee57c4ddc0f780d81e
    type: cleaning
    trigger: device
  conditions: []
  actions:
  - alias: Guard Count
    if:
    - condition: numeric_state
      entity_id: counter.robot_count
      above: 1
    then:
    - action: counter.reset
      metadata: {}
      data: {}
      target:
        entity_id: counter.robot_count
    - stop: ''
  - alias: Wait Discharge
    wait_for_trigger:
    - trigger: numeric_state
      entity_id:
      - sensor.robot_battery
      below: 80
  - parallel:
    - device_id: d7e8029ab6b34ff6a352c21a557157c5
      domain: vacuum
      entity_id: 0f6c7891f3b129ee57c4ddc0f780d81e
      type: dock
    - action: counter.increment
      metadata: {}
      data: {}
      target:
        entity_id: counter.robot_count
  - wait_for_trigger:
    - trigger: numeric_state
      entity_id:
      - sensor.robot_battery
      above: 99
    alias: Wait Charged
  - device_id: d7e8029ab6b34ff6a352c21a557157c5
    domain: vacuum
    entity_id: 0f6c7891f3b129ee57c4ddc0f780d81e
    type: clean
  mode: single
- id: '1730220382533'
  alias: Control - Leave - Robot Out
  description: ''
  triggers:
  - entity_id:
    - input_boolean.location_daniel_home
    to: 'off'
    for:
      hours: 0
      minutes: 0
      seconds: 0
    trigger: state
  conditions:
  - condition: numeric_state
    entity_id: counter.robot_count
    below: 2
  actions:
  - if:
    - condition: numeric_state
      entity_id: sensor.robot_battery
      below: 99
    then:
    - wait_for_trigger:
      - trigger: numeric_state
        entity_id: []
        above: 99.6
    alias: Wait until robot is charged
  - device_id: d7e8029ab6b34ff6a352c21a557157c5
    domain: vacuum
    entity_id: 0f6c7891f3b129ee57c4ddc0f780d81e
    type: clean
  mode: single
- id: '1730655071916'
  alias: Control - Home Assistant - Reset lights
  description: ''
  triggers:
  - event: start
    trigger: homeassistant
  conditions: []
  actions:
  - action: light.turn_off
    metadata: {}
    data: {}
    target:
      area_id:
      - balcony
      - bedroom
      - play_room
      - living_room
      - main_bathroom
      - clothes_zone
      - kitchen
      - studio
  mode: single
- id: '1730935078924'
  alias: Control - Pomodoro
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - input_boolean.is_pomodoro_running
    to: 'on'
    id: 'on'
  - trigger: state
    entity_id:
    - input_boolean.is_pomodoro_running
    to: 'off'
    id: 'Off'
  - trigger: event
    event_type: timer.finished
    id: relax_idle
    event_data:
      entity_id: timer.pomodoro_relax_timer
  - trigger: event
    event_type: timer.finished
    id: focus_idle
    event_data:
      entity_id: timer.pomodoro_focus_timer
  conditions: []
  actions:
  - alias: 'On'
    if:
    - condition: trigger
      id:
      - 'on'
    then:
    - parallel:
      - action: script.speak
        data:
          message: 'El Pomodoro ha comenzado '
      - data: {}
        action: input_boolean.turn_on
        target:
          entity_id: input_boolean.is_pomodoro_running
      - action: script.phone_do_not_disturb_on
        data: {}
      - action: script.active_area_lights_on
        metadata: {}
        data: {}
      - action: timer.start
        metadata: {}
        data: {}
        target:
          entity_id: timer.pomodoro_focus_timer
      - action: script.habitica_score_task_2
        metadata: {}
        data:
          task_name: Usar pomodoro
  - alias: Focus Idle
    if:
    - condition: trigger
      id:
      - focus_idle
    then:
    - parallel:
      - action: script.speak
        data:
          message: El pomodoro ha terminado
      - action: script.phone_do_not_disturb_off
        data: {}
      - if:
        - condition: time
          after: '18:00:00'
          before: 06:00:00
        then:
        - data: {}
          target:
            area_id:
            - kitchen
            - living_room
          action: light.turn_on
      - action: timer.start
        metadata: {}
        data: {}
        target:
          entity_id: timer.pomodoro_relax_timer
      - data: {}
        action: input_boolean.turn_off
        target:
          entity_id: input_boolean.is_pomodoro_running
  - alias: Relax Idle
    if:
    - condition: trigger
      id:
      - relax_idle
    then:
    - parallel:
      - action: script.active_area_lights_on
        metadata: {}
        data: {}
      - action: script.speak
        metadata: {}
        data:
          message: Es hora de volver campeon
  - alias: 'Off'
    if:
    - condition: trigger
      id:
      - 'Off'
    then:
    - action: timer.cancel
      metadata: {}
      data: {}
      target:
        entity_id:
        - timer.pomodoro_focus_timer
    - action: timer.cancel
      metadata: {}
      data: {}
      target:
        entity_id:
        - timer.pomodoro_relax_timer
    enabled: false
  mode: restart
- id: '1731259601985'
  alias: Score - Excercise
  description: ''
  triggers:
  - trigger: numeric_state
    entity_id:
    - sensor.heart_points_daily
    above: 20
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: Ejercicio 1 session
  mode: single
- id: '1731264292861'
  alias: Score - Meditate
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: com.getsomeheadspace.android
    for:
      hours: 0
      minutes: 10
      seconds: 0
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: com.spotlightsix.zentimerlite2
    for:
      hours: 0
      minutes: 10
      seconds: 0
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: Meditar
  mode: single
- id: '1731336319648'
  alias: Score - Pomodoro ON
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - input_boolean.is_pomodoro_running
    to: 'on'
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: Usar Pomodoro
  mode: single
- id: '1731344359980'
  alias: 'Score - Clean '
  description: ''
  triggers:
  - entity_id:
    - vacuum.robot
    to: cleaning
    trigger: state
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: 'Home: barrer'
  mode: restart
- id: '1731344660795'
  alias: Score - Washer
  description: ''
  triggers:
  - entity_id:
    - sensor.washer
    to: 'off'
    trigger: state
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: 'Home: Lavar Ropa'
  mode: single
- id: '1731344990487'
  alias: Score - Wake Up
  description: ''
  triggers:
  - type: not_opened
    device_id: 55c8d5cb3c315726d8ed170fb02039ca
    entity_id: 78cae655efed33ed617b7c8f228bd154
    domain: binary_sensor
    trigger: device
  conditions: []
  actions:
  - if:
    - condition: time
      after: 04:00:00
      before: 08:00:00
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: Bañarme temprano
  mode: restart
- id: '1731345230640'
  alias: Score - TV and Work Not Late
  description: ''
  triggers:
  - trigger: time
    at: '23:59:00'
    id: UN CHECK
  conditions: []
  actions:
  - if:
    - condition: or
      conditions:
      - condition: state
        entity_id: input_boolean.juegos_tv
        state: 'off'
        for:
          hours: 3
          minutes: 0
          seconds: 0
      - condition: state
        entity_id: input_boolean.trabajo_switch
        state: 'off'
        for:
          hours: 3
          minutes: 0
          seconds: 0
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: 'TV: no prender tarde'
    else:
    - stop: It was turned on
  mode: single
- id: '1731353966324'
  alias: Score - Read Blogs
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: com.devhd.feedly
    for:
      hours: 0
      minutes: 5
      seconds: 0
  - trigger: state
    entity_id:
    - sensor.samsung_tab_s6l_last_used_app
    to: com.medium.reader
    for:
      hours: 0
      minutes: 5
      seconds: 0
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: com.medium.reader
    for:
      hours: 0
      minutes: 5
      seconds: 0
  - trigger: state
    entity_id:
    - sensor.samsung_tab_s6l_last_used_app
    to: com.devhd.feedly
    for:
      hours: 0
      minutes: 5
      seconds: 0
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: Actualizarme con blogs
  mode: single
- id: '1731354194080'
  alias: Score - Read or Work
  description: ''
  triggers:
  - entity_id:
    - input_boolean.is_pomodoro_running
    to: 'on'
    trigger: state
    id: POMODORO
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: com.google.android.apps.books
    for:
      hours: 0
      minutes: 10
      seconds: 0
    id: APP
  - trigger: state
    entity_id:
    - sensor.samsung_tab_s6l_last_used_app
    to: com.google.android.apps.books
    for:
      hours: 0
      minutes: 10
      seconds: 0
    id: APP
  conditions: []
  actions:
  - if:
    - type: is_occupied
      condition: device
      device_id: fed95d4e3ab0ebdfdc6bd19dedb8cc54
      entity_id: 1a2320a50b807697de9c4580527fc8cf
      domain: binary_sensor
    - condition: trigger
      id:
      - APP
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: 'Leer Libro '
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: Leer
    else:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: Trabajar
  mode: single
- id: '1731364956361'
  alias: Notify - Robot got problems
  description: ''
  triggers:
  - type: battery_level
    device_id: d7e8029ab6b34ff6a352c21a557157c5
    entity_id: 828201ed5c76e4c0aa4218d27564529a
    domain: sensor
    trigger: device
    below: 10
  - trigger: state
    entity_id:
    - vacuum.robot
    to: error
  conditions: []
  actions:
  - action: script.send_push_notification
    data:
      message: Robot got problema
      title: Status
  mode: single
- id: '1731439148837'
  alias: Score - Coursera
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_used_app
    to: org.coursera.android
    for:
      hours: 0
      minutes: 10
      seconds: 0
  - trigger: state
    entity_id:
    - sensor.samsung_tab_s6l_last_used_app
    to: org.coursera.android
    for:
      hours: 0
      minutes: 10
      seconds: 0
  conditions: []
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: Estudiar
  mode: single
- id: '1731523571456'
  alias: Notify - Pico y Placa
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - input_boolean.puerta_principal
    to: 'off'
    id: puerta
  conditions:
  - condition: time
    after: 05:00:00
    before: '20:00:00'
    weekday:
    - thu
  actions:
  - action: notify.mobile_app_pixel_9_pro
    metadata: {}
    data:
      title: PICO Y PLACA
      message: ALERTA
  - action: notify.mobile_app_pixel_9_pro
    metadata: {}
    data:
      title: PICO Y PLACA
      message: ALERTA
  - if:
    - condition: trigger
      id:
      - puerta
    then:
    - action: script.speak
      metadata: {}
      data:
        message: ALERTA, Pico y placa
  mode: single
- id: '1731527406965'
  alias: 'Score - Respetar Pico y Placa '
  description: ''
  triggers:
  - trigger: time
    at: '20:00:00'
    id: UN CHECK
  conditions:
  - condition: time
    after: 05:00:00
    before: '20:00:00'
    weekday:
    - wed
  actions:
  - if:
    - condition: state
      entity_id: binary_sensor.pixel_9_pro_android_auto
      state: 'off'
      for:
        hours: 20
        minutes: 0
        seconds: 0
    then:
    - action: script.habitica_score_task_2
      metadata: {}
      data:
        task_name: No usar carro en pico y placa
    else:
    - stop: It was turned on
  mode: single
- id: '1731897873798'
  alias: Task - Create TODOs Tasks to Score
  description: ''
  triggers:
  - hours: '9'
    trigger: time_pattern
  conditions:
  - condition: template
    value_template: '{{ now().day == 1 }}'
    enabled: true
  actions:
  - variables:
      current_month_year: '{{ now().strftime(''%Y %B'') }}'
      current_month: '{{ now().month }}'
      next_month_year: '{% set next_month = (now().replace(day=1) + timedelta(days=32)).replace(day=1)
        %} {{ next_month.strftime(''%Y %B'') }}

        '
      next_month: '{% set next_month_date = (now().replace(day=1) + timedelta(days=32)).replace(day=1)
        %} {{ next_month_date.month }}

        '
      tasks:
      - Pagar Admin
      - Pagar EPM
      - Pagar DEV - Copilot
      - Pagar DEV - Chat GPT
      - Pagar FUN - Youtube
      - Pagar GYM
      bathroom_tasks:
      - Limpiar Ducha
      - Limpiar Sanitarios
      clean_tasks:
      - Trapear
  - action: script.habitica_create_task
    data:
      tag_name: Homework
      due_day_month: 5
      due_month_number: '{{ next_month }}'
      task_name: Pagar Servicios EPM {{ current_month_year }}
    enabled: true
  - action: script.habitica_create_task
    data:
      tag_name: Homework
      due_day_month: 15
      due_month_number: '{{ current_month }}'
      task_name: Pagar TC Scotia {{ current_month_year }}
    enabled: true
  - repeat:
      for_each: '{{ tasks }}'
      sequence:
      - data:
          tag_name: Homework
          due_month_number: '{{ current_month }}'
          due_day_month: 25
          task_name: '{{ repeat.item }} {{ current_month_year }}'
        action: script.habitica_create_task
    enabled: true
  - repeat:
      for_each: '{{ bathroom_tasks }}'
      sequence:
      - data:
          tag_name: Homework
          due_day_month: 15
          due_month_number: '{{ current_month }}'
          task_name: '{{ repeat.item }} {{ current_month_year }} 15'
        action: script.habitica_create_task
      - data:
          tag_name: Homework
          due_day_month: 28
          due_month_number: '{{ current_month }}'
          task_name: '{{ repeat.item }} {{ current_month_year }} 28'
        action: script.habitica_create_task
  - repeat:
      for_each: '{{ clean_tasks }}'
      sequence:
      - data:
          tag_name: Homework
          due_day_month: 8
          due_month_number: '{{ current_month }}'
          task_name: '{{ repeat.item }} {{ current_month_year }} 8'
        action: script.habitica_create_task
      - data:
          tag_name: Homework
          due_day_month: 16
          due_month_number: '{{ current_month }}'
          task_name: '{{ repeat.item }} {{ current_month_year }} 16'
        action: script.habitica_create_task
      - data:
          tag_name: Homework
          due_day_month: 24
          due_month_number: '{{ current_month }}'
          task_name: '{{ repeat.item }} {{ current_month_year }} 24'
        action: script.habitica_create_task
  mode: single
- id: '1731900973244'
  alias: Control - Charge - Tablet s6l
  description: ''
  triggers:
  - type: battery_level
    device_id: 7bccb6f687937ca398bad460107bcd47
    entity_id: e276c2a9fbeba42f30da96332783c5a5
    domain: sensor
    trigger: device
    above: 99
  conditions: []
  actions:
  - action: switch.turn_off
    metadata: {}
    data: {}
    target:
      entity_id: switch.regleta_l5
  mode: single
- id: '1732297674018'
  alias: Score - Reward - TV
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - input_boolean.juegos_tv
    to: 'on'
    for:
      hours: 0
      minutes: 10
      seconds: 0
    from: 'off'
  conditions:
  - condition: not
    conditions:
    - condition: time
      weekday:
      - sat
      - sun
  actions:
  - action: script.habitica_score_task_2
    metadata: {}
    data:
      task_name: PRueba
  mode: restart
- id: '1734548341382'
  alias: Notify - Speak - Calendar - Meetings
  description: ''
  triggers:
  - trigger: calendar
    entity_id: calendar.daniel_neonscreens_com
    event: start
    offset: -0:1:0
    id: NEON
  - trigger: calendar
    entity_id: calendar.danielgomezrico_gmail_com
    event: start
    offset: '-1:0:0'
    id: ME
  conditions: []
  actions:
  - if:
    - condition: trigger
      id:
      - NEON
    then:
    - action: script.speak
      metadata: {}
      data:
        message: Alerta, Alerta, Reunion de Neon esta por comenzar {{ trigger.calendar_event.summary
          }}
    else:
    - action: script.speak
      metadata: {}
      data:
        message: Dani cuidado, falta 1 hora para tu compromiso {{ trigger.calendar_event.summary
          }}
  mode: single
- id: '1736112421134'
  alias: Status - location - group Mode - sync
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - input_boolean.location_daniel_home
  - trigger: state
    entity_id:
    - input_boolean.location_nati_home
  conditions: []
  actions:
  - if:
    - condition: state
      entity_id: input_boolean.location_daniel_home
      state: 'on'
    - condition: state
      entity_id: input_boolean.location_nati_home
      state: 'on'
    then:
    - action: input_boolean.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: input_boolean.group_mode
    else:
    - action: input_boolean.turn_off
      metadata: {}
      data: {}
      target:
        entity_id: input_boolean.group_mode
  mode: single
- id: '1737777103967'
  alias: sand
  description: ''
  triggers: []
  conditions: []
  actions:
  - parallel:
    - action: script.blink_light_living
      metadata: {}
      data: {}
    - sequence:
      - action: fan.turn_on
        metadata: {}
        data: {}
        target:
          area_id: studio
      - delay:
          hours: 0
          minutes: 0
          seconds: 8
      - action: fan.turn_off
        metadata: {}
        data: {}
        target:
          area_id: studio
      - action: light.turn_on
        metadata: {}
        data: {}
        target:
          entity_id: light.living_light_left
  mode: single
- id: '1738330834489'
  alias: Control - Work Mode - Mac
  description: ''
  triggers:
  - type: turned_on
    device_id: e515d4e283e904e0fa27d06a0b298b87
    entity_id: bb5b442b4f8c745be2a9ff1fa73d0c68
    domain: binary_sensor
    trigger: device
    id: 'ON'
  - type: turned_off
    device_id: e515d4e283e904e0fa27d06a0b298b87
    entity_id: bb5b442b4f8c745be2a9ff1fa73d0c68
    domain: binary_sensor
    trigger: device
    id: 'OFF'
    for:
      hours: 0
      minutes: 0
      seconds: 0
    enabled: false
  conditions: []
  actions:
  - if:
    - condition: trigger
      id:
      - 'ON'
    then:
    - action: input_boolean.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: input_boolean.trabajo_switch
    else:
    - action: input_boolean.turn_off
      metadata: {}
      data: {}
      target:
        entity_id: input_boolean.trabajo_switch
  mode: single
- id: '1738431005531'
  alias: Presence - Kitchen - Control
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.kitchen_presence_presence_event
    to: approach
    id: ENTER_KITCHEN
  - trigger: state
    entity_id:
    - sensor.kitchen_presence_presence_event
    to: leave
    id: LEAVE
  conditions:
  - condition: sun
    after: sunset
    before: sunrise
  actions:
  - if:
    - condition: trigger
      id:
      - ENTER_KITCHEN
    then:
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_left
  - if:
    - condition: trigger
      id:
      - ENTER_BAR
    then:
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_center
  - if:
    - condition: trigger
      id:
      - BAR LEAVE
      - LEAVE
    then:
    - action: light.turn_off
      metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_center
  - if:
    - condition: trigger
      id:
      - KITCHEN LEAVE
      - LEAVE
    then:
    - action: light.turn_off
      metadata: {}
      data: {}
      target:
        entity_id: light.kitchen_light_left
  mode: single
- id: '1738439903768'
  alias: Kitchen - Waffle - Safe Control
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - switch.regleta_l3
    to: 'on'
  conditions: []
  actions:
  - delay:
      hours: 0
      minutes: 18
      seconds: 0
      milliseconds: 0
  - action: switch.turn_off
    metadata: {}
    data: {}
    target:
      entity_id: switch.regleta_l3
  - action: script.speak
    metadata: {}
    data:
      message: Waffle was turned off
  mode: single
- id: '1742653325600'
  alias: Control - Games - RF health on
  description: ''
  triggers:
  - trigger: homeassistant
    event: start
  conditions: []
  actions:
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - type: turn_off
    device_id: c0dd636f3547a23fee535cefcb197a6c
    entity_id: c322197c161380b8890918d84841e6ec
    domain: switch
    metadata:
      secondary: false
  - delay:
      hours: 0
      minutes: 0
      seconds: 10
      milliseconds: 0
  - type: turn_on
    device_id: c0dd636f3547a23fee535cefcb197a6c
    entity_id: c322197c161380b8890918d84841e6ec
    domain: switch
  mode: single
- id: '1742754767969'
  alias: Push Notification - On Push Notification - Register Expense
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_notification
  conditions:
  - condition: not
    conditions:
    - condition: state
      entity_id: sensor.pixel_9_pro_last_notification
      attribute: package
      state: com.whatsapp
  actions:
  - data:
      title: '{{ state_attr(''sensor.pixel_9_pro_last_notification'', ''android.title'')
        }}'
      content: '{{ state_attr(''sensor.pixel_9_pro_last_notification'', ''android.text'')
        }}'
      app: '{{ state_attr(''sensor.pixel_9_pro_last_notification'', ''package'') }}'
    action: script.money_register_money_expense_from_notification
  - action: script.notify_phone
    metadata: {}
    data:
      title: Gasto Registrado
      message: '{{ state_attr(''sensor.pixel_9_pro_last_notification'', ''android.text'')
        }}'
    enabled: false
  mode: single
- id: '1742765278576'
  alias: Notify - Porteria Llamando
  description: ''
  triggers:
  - trigger: state
    entity_id:
    - sensor.pixel_9_pro_last_notification
    attribute: android.text
    to: Incoming voice call
  conditions:
  - condition: state
    entity_id: sensor.pixel_9_pro_last_notification
    attribute: android.title
    state: Porteria Oz
    enabled: true
  actions:
  - action: script.speak
    metadata: {}
    data:
      message: Dani, te llaman de porteria, cuidadimbiri
  mode: single
