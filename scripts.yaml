make_coffee:
  alias: Make - Coffee
  sequence:
  - service: switch.turn_on
    data: {}
    target:
      entity_id: switch.power_strip_coffee_grinder
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: Tienes 3 minutos para preparar el cafe y contando!
      language: es
      cache: false
  - delay:
      hours: 0
      minutes: 3
      seconds: 0
      milliseconds: 0
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: Prendiendo cafetera, a volar la imaginación!
      language: es
      cache: false
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.power_strip_coffee_grinder
  - service: switch.turn_on
    data: {}
    target:
      entity_id: switch.power_strip_switch_coffee_maker
  - delay:
      hours: 0
      minutes: 10
      seconds: 0
      milliseconds: 0
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.power_strip_switch_coffee_maker
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: El cafe ya debe estar listo!
      cache: false
      language: es
  mode: single
  icon: mdi:coffee
play_blackout_up:
  alias: Roller - Blackout - Play - Up
  sequence:
  - service: remote.send_command
    data:
      device: play_blackout
      command: up
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:roller-shade
play_blackout_down:
  alias: Roller - Blackout - Play - Down
  sequence:
  - if:
    - condition: state
      entity_id: input_boolean.ventana_juegos
      state: 'on'
    then:
    - data:
        device: play_blackout
        command: down
      target:
        device_id: a543756c4a011ac9403242b166c01ad4
      action: remote.send_command
    else:
    - action: script.speak
      data:
        message: No se puede bajar el games blackout
    - action: script.send_push_notification
      data:
        message: No se puede bajar blackout
        title: ALERTA - Ventana Juegos Abierta
  mode: single
  icon: mdi:roller-shade-closed
play_blackout_stop:
  alias: Roller - Blackout - Play - Stop
  sequence:
  - service: remote.send_command
    data:
      device: play_blackout
      command: stop
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:close-octagon
main_blackout_down:
  alias: Roller - Blackout - Main - Down
  sequence:
  - if:
    - condition: state
      entity_id: input_boolean.ventana_habitacion_principal
      state: 'on'
    then:
    - data:
        device: main_blackout
        command: down
      target:
        device_id: a543756c4a011ac9403242b166c01ad4
      action: remote.send_command
    else:
    - action: script.speak
      data:
        message: Ventana principal esta abierta, el blackout no puede bajar
    - action: script.send_push_notification
      data:
        message: No se puede bajar blackout
        title: ALERTA - Ventana Principal Abierta
  mode: single
  icon: mdi:roller-shade-closed
main_blackout_stop:
  alias: Roller - Blackout - Main - Stop
  sequence:
  - service: remote.send_command
    data:
      device: main_blackout
      command: stop
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:close-octagon
main_blackout_up:
  alias: Roller - Blackout - Main - Up
  sequence:
  - service: remote.send_command
    data:
      device: main_blackout
      command: up
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:roller-shade
blackouts_up:
  alias: Rollers - Up
  sequence:
  - parallel:
    - service: script.play_blackout_up
      data: {}
    - service: script.main_blackout_up
      data: {}
    - service: script.main_curtain_up
      data: {}
    alias: Blackouts Up
  mode: single
  icon: mdi:blinds-open
blackouts_down:
  alias: Rollers - Down
  sequence:
  - alias: Blackouts Down
    parallel:
    - service: script.play_blackout_down
      data: {}
    - service: script.main_blackout_down
      data: {}
    - service: script.roller_curtain_main_down
      data: {}
  mode: single
  icon: mdi:roller-shade-closed
blackouts_middle:
  alias: Rollers - Middle
  sequence:
  - alias: Blackouts Down
    parallel:
    - service: script.play_blackout_down
      data: {}
    - service: script.main_blackout_down
      data: {}
    - service: script.roller_curtain_main_down
      data: {}
  - delay:
      hours: 0
      minutes: 0
      seconds: 20
      milliseconds: 0
    alias: Wait to be down
  - parallel:
    - service: script.play_blackout_up
      data: {}
    - service: script.main_blackout_up
      data: {}
    alias: Blackouts Up
  - delay:
      hours: 0
      minutes: 0
      seconds: 4
      milliseconds: 0
  - parallel:
    - service: script.main_blackout_stop
      data: {}
    - service: script.play_blackout_stop
      data: {}
    alias: 'Blackouts Stop '
  mode: single
  icon: mdi:roller-shade
main_curtain_stop:
  alias: Roller - Curtain - Main - Stop
  sequence:
  - service: remote.send_command
    data:
      device: main_curtain
      command: stop
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:close-octagon
main_curtain_up:
  alias: Roller - Curtain - Main - Up
  sequence:
  - service: remote.send_command
    data:
      device: main_curtain
      command: up
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:roller-shade
day_mode:
  alias: Mode - Day
  sequence:
  - parallel:
    - service: script.play_blackout_down
      data: {}
    - service: script.main_blackout_up
      data: {}
    - service: script.main_curtain_down
      data: {}
    alias: Blackouts Down
  mode: single
  icon: mdi:roller-shade-closed
play_sound_volume_up:
  alias: Play - Sound - Volume Up
  sequence:
  - service: remote.send_command
    data:
      command: volume_up
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:volume-plus
play_sound_volume_down:
  alias: Play - Sound - Volume Down
  sequence:
  - service: media_player.volume_set
    target:
      entity_id: media_player.speakers
    data:
      volume_level: 0.3
  mode: single
  icon: mdi:volume-minus
play_sound_level:
  alias: Play - Sound - Level
  sequence:
  - service: remote.send_command
    data:
      command: level
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:surround-sound
play_sound_effect:
  alias: Play - Sound - Effect
  sequence:
  - service: remote.send_command
    data:
      command: effect
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:volume-vibrate
play_sound_power:
  alias: Play - Sound - Power
  sequence:
  - service: remote.send_command
    data:
      command: power
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:power
play_sound_input:
  alias: Play - Sound - Input
  sequence:
  - service: remote.send_command
    data:
      command: input
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:volume-source
play_sound_mute:
  alias: Play - Sound - Mute
  sequence:
  - service: remote.send_command
    data:
      command: mute
      device: play_sound
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:volume-off
play_tv_power:
  alias: Games - TV - Power Status Change
  sequence:
  - service: remote.send_command
    data:
      device: play_tv
      command: power
    target:
      device_id: a543756c4a011ac9403242b166c01ad4
  mode: single
  icon: mdi:power
moviemode_off:
  alias: Mode - Movie - Off
  sequence:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.juegos_tv
  mode: single
  icon: mdi:movie
movie_mode_on:
  alias: Mode - Movie - On
  sequence:
  - parallel:
    - service: input_boolean.turn_on
      data: {}
      target:
        entity_id: input_boolean.juegos_tv
  mode: single
  icon: mdi:movie
work_mode_on:
  alias: Mode - Work - Off
  sequence:
  - service: input_boolean.turn_off
    data: {}
    target:
      entity_id: input_boolean.trabajo_switch
  mode: single
  icon: mdi:laptop
work_mode_on_2:
  alias: Mode - Work - On
  sequence:
  - service: input_boolean.turn_on
    data: {}
    target:
      entity_id: input_boolean.trabajo_switch
  mode: single
  icon: mdi:laptop
music_mode_off:
  alias: Mode - Multimedia - Off
  sequence:
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.studio_music_switch
  mode: single
  icon: mdi:music
music_mode_on:
  alias: Mode - Multimedia - On
  sequence:
  - service: switch.turn_on
    data: {}
    target:
      entity_id:
      - switch.computador_linux
      - switch.studio_music_switch
  mode: single
  icon: mdi:music
play_blackout_up_safe:
  alias: Play Blackout Up Safe
  sequence:
  - if:
    - condition: sun
      after: sunset
    then:
    - service: script.play_blackout_up
      data: {}
    enabled: false
  - service: script.play_blackout_up
    data: {}
  mode: single
  icon: mdi:roller-shade-closed
shower_mode_on:
  alias: Mode - Shower - On
  sequence:
  - parallel:
    - data: {}
      action: script.main_blackout_down
    - data: {}
      action: script.roller_curtain_main_down
    - data: {}
      target:
        area_id:
        - bedroom
        - play_room
        - kitchen
        - living_room
        - studio
        - clothes_zone
      action: light.turn_off
    - metadata: {}
      data: {}
      target:
        entity_id: timer.shower_timer
      action: timer.start
    - action: light.turn_on
      metadata: {}
      data:
        effect: colorloop
      target:
        area_id: main_bathroom
    - action: music_assistant.play_media
      metadata: {}
      data:
        media_type: artist
        media_id: Snarky puppy
      target:
        area_id:
        - main_bathroom
  - wait_for_trigger:
    - type: opened
      device_id: d1ab74b56977fe5714a18bfec56ed865
      entity_id: e6595bc28cc35bf5ef3b121b6830b20b
      domain: binary_sensor
      trigger: device
    alias: Wait Open Door
  - target:
      entity_id:
      - timer.shower_timer
    data: {}
    action: timer.cancel
  - metadata: {}
    data: {}
    action: script.mode_shower_off
read_mode_on:
  alias: Mode - Read - On
  sequence:
  - parallel:
    - service: light.turn_off
      data: {}
      target:
        area_id:
        - bedroom
        - kitchen
        - main_bathroom
        - play_room
        - studio
        - balcony
    - service: light.turn_on
      data: {}
      target:
        entity_id: light.sala_light
  mode: single
read_mode_balcon_on_2:
  alias: Mode - Read - Balcony - On
  sequence:
  - service: light.turn_off
    data: {}
    target:
      area_id:
      - bedroom
      - kitchen
      - living_room
      - play_room
      - studio
      - dressing_room
      - main_bathroom
  - service: light.turn_on
    data: {}
    target:
      entity_id: light.balcon_bombillo_dos
  - service: light.turn_off
    metadata: {}
    data: {}
    target:
      entity_id: light.balcon_bombillo
  mode: single
modo_cocina_on:
  alias: Mode - Cocina  - On
  sequence:
  - parallel:
    - service: light.turn_off
      data: {}
      target:
        area_id:
        - balcony
        - bedroom
        - living_room
        - main_bathroom
        - play_room
        - studio
    - service: light.turn_on
      data: {}
      target:
        area_id: kitchen
  mode: single
  icon: mdi:chef-hat
make_tea:
  alias: Make - Tea
  sequence:
  - service: switch.turn_on
    data: {}
    target:
      entity_id:
      - switch.power_strip_switch_kettle
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      language: es
      message: Calentando agua para tu aromática
  - delay:
      hours: 0
      minutes: 6
      seconds: 0
      milliseconds: 0
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: Tal vez... tu aromática puede prepararse ya
      language: es
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.power_strip_switch_kettle
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: 'Apagando hervidor '
  mode: single
  icon: mdi:tea
mode_pomodoro_on:
  alias: Mode - Pomodoro - ON
  sequence:
  - action: input_boolean.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: input_boolean.is_pomodoro_running
  icon: mdi:clock-time-eight-outline
  mode: restart
  description: ''
make_cargar_tablet:
  alias: Make - Charge - Tablet
  sequence:
  - service: switch.turn_on
    data: {}
    target:
      entity_id: switch.power_strip_switch_5
  - wait_for_trigger:
    - type: battery_level
      platform: device
      device_id: fce12288d8fa7b1d2187770eb4a827a3
      entity_id: 44095028a176833a10d59be8d3ae7109
      domain: sensor
      above: 99
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.power_strip_switch_5
  mode: single
shutdown_all:
  alias: Shutdown - All
  sequence:
  - service: light.turn_off
    data: {}
    target:
      area_id:
      - balcony
      - bedroom
      - living_room
      - main_bathroom
      - play_room
      - kitchen
      - studio
  - service: script.music_mode_off
    data: {}
  - service: script.work_mode_on
    data: {}
  - service: script.moviemode_off
    data: {}
  - service: switch.turn_off
    data: {}
    target:
      device_id: 013df7d707c17d16b9a08d98f6b12185
  mode: single
roller_curtain_main_down:
  alias: Roller - Curtain - Main - Down
  sequence:
  - if:
    - condition: state
      entity_id: input_boolean.ventana_habitacion_principal
      state: 'on'
    then:
    - service: remote.send_command
      data:
        device: main_curtain
        command: down
      target:
        device_id: a543756c4a011ac9403242b166c01ad4
    else:
    - service: tts.google_translate_say
      data:
        cache: false
        language: es
        entity_id: media_player.speakers
        message: Transmitir "Alerta! no se puede bajar la cortina principal, la ventana
          esta abierta"
    - action: script.send_push_notification
      data:
        message: No se puede bajar blackout
        title: ALERTA - Ventana Principal Abierta
  mode: single
  icon: mdi:roller-shade-closed
play_sound_zombie:
  alias: Play - Sound - Zombie
  sequence:
  - service: media_extractor.play_media
    data:
      speaker: "{{ [\n    'media_player.speakers'\n] | random }} \n"
      volume: 0.7
      media: "{{- [\n \"https://www.youtube.com/watch?v=CzcgWV7-YCQ\",\n \"https://www.youtube.com/watch?v=w2h_Oz9cnEo\"\n
        \ ] | random -}}\n"
  mode: single
control_sonido_volumen_bajo:
  alias: Mode - Home - Volume - Low
  sequence:
  - service: media_player.volume_set
    target:
      entity_id: media_player.speakers
    data:
      volume_level: 0.3
  mode: single
control_sonido_volumen_medio:
  alias: Mode - Home - Volume - Medium
  sequence:
  - service: media_player.volume_set
    target:
      entity_id: media_player.speakers
    data:
      volume_level: 0.5
  mode: single
control_sonido_volumen_alto:
  alias: Mode - Home - Volume - High
  sequence:
  - service: media_player.volume_set
    target:
      entity_id: media_player.speakers
    data:
      volume_level: 0.75
  mode: single
fingerbot_giro_left:
  alias: FingerBot - Giro - Left
  sequence:
  - service: remote.send_command
    metadata: {}
    data:
      device: fingerbot_giro
      command: left
      num_repeats: 10
      hold_secs: 1
    target:
      entity_id: remote.ir
  mode: single
fingerbot_giro_right:
  alias: FingerBot - Giro - Right
  sequence:
  - service: remote.send_command
    metadata: {}
    data:
      device: fingerbot_giro
      command: right
      num_repeats: 10
      hold_secs: 1
    target:
      entity_id: remote.ir
  mode: single
robot_go_back_to_base:
  alias: Robot - Go Back To Base
  sequence:
  - data: {}
    target:
      entity_id: vacuum.robot
    action: vacuum.return_to_base
  - action: script.send_push_notification
    data:
      message: El robot no puede limpiar por que el balcón esta abierto
      title: ALERT
  mode: single
  icon: mdi:robot-vacuum-off
modo_individual_on:
  alias: Modo - Individual - PRENDER
  sequence:
  - service: input_boolean.turn_on
    metadata: {}
    data: {}
    target:
      entity_id: input_boolean.modo_individual
  mode: single
modo_individual_off:
  alias: Modo - Individual - APAGAR
  sequence:
  - service: input_boolean.turn_off
    target:
      entity_id:
      - input_boolean.modo_individual
    data: {}
  mode: single
make_coffee_with_coffee_maker:
  alias: Make - Coffee With Coffee Maker
  sequence:
  - service: switch.turn_on
    data: {}
    target:
      entity_id: switch.0x70b3d52b60014e3d_l4
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: Tienes 3 minutos para preparar el cafe y contando!
      language: es
      cache: false
  - delay:
      hours: 0
      minutes: 3
      seconds: 0
      milliseconds: 0
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: Prendiendo cafetera
      language: es
      cache: false
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.0x70b3d52b60014e3d_l4
  - service: switch.turn_on
    data: {}
    target:
      entity_id: switch.0x70b3d52b60014e3d_l2
  - delay:
      hours: 0
      minutes: 10
      seconds: 0
      milliseconds: 0
  - service: switch.turn_off
    data: {}
    target:
      entity_id: switch.0x70b3d52b60014e3d_l2
  - service: tts.google_translate_say
    data:
      entity_id: media_player.speakers
      message: El cafe ya debe estar listo!
      cache: false
      language: es
  mode: single
  icon: mdi:coffee-maker
blink_light_home:
  alias: Blink Light - Home
  use_blueprint:
    path: paulo-graca/script-blink-light.yaml
    input:
      light_selector:
        area_id:
        - living_room
        - balcony
      delay_duration: 0.5
      repetitions: 6
play_roller_middle:
  alias: Play - Roller - Middle
  sequence:
  - alias: Blackouts Down
    parallel:
    - service: script.play_blackout_down
      data: {}
  - delay:
      hours: 0
      minutes: 0
      seconds: 20
      milliseconds: 0
    alias: Wait to be down
  - alias: Blackouts Up
    parallel:
    - service: script.play_blackout_up
      data: {}
  - delay:
      hours: 0
      minutes: 0
      seconds: 4
      milliseconds: 0
  - alias: 'Blackouts Stop '
    parallel:
    - service: script.play_blackout_stop
      data: {}
  mode: single
  icon: mdi:roller-shade
blink_light_bathroom:
  alias: Blink Light - Bathroom
  use_blueprint:
    path: paulo-graca/script-blink-light.yaml
    input:
      light_selector:
        area_id: main_bathroom
      delay_duration: 0.3
      repetitions: 3
mode_shower_off:
  alias: Mode - Shower - Off
  sequence:
  - parallel:
    - type: turn_off
      device_id: b66b33fcdb579e9c2f56acdd68449eb0
      entity_id: e89c0e79f312f4133521e3cb63a7aa87
      domain: light
    - metadata: {}
      data: {}
      target:
        area_id: main_bathroom
      action: media_player.media_stop
    - type: turn_on
      device_id: ee794228b3d0aa720e68aa4280632e1c
      entity_id: 3beb5953a7f09dd99242f9f353d21c93
      domain: switch
    - if:
      - condition: sun
        before: sunset
        after: sunrise
      then:
      - data: {}
        action: script.main_blackout_up
  - delay:
      hours: 0
      minutes: 0
      seconds: 30
      milliseconds: 0
  - type: turn_off
    device_id: ee794228b3d0aa720e68aa4280632e1c
    entity_id: 3beb5953a7f09dd99242f9f353d21c93
    domain: switch
  - delay:
      hours: 0
      minutes: 2
      seconds: 0
      milliseconds: 0
  - if:
    - condition: time
      after: 05:00:00
      before: '17:00:00'
    then:
    - data: {}
      action: script.main_curtain_up
  mode: restart
  icon: mdi:shower-head
  description: ''
phone_do_not_disturb_on:
  alias: Home - DND - ON
  sequence:
  - parallel:
    - action: shell_command.ubuntu_dnd_sync
      metadata: {}
      data: {}
    - action: notify.mobile_app_pixel_9_pro
      metadata: {}
      data:
        message: command_dnd
        data:
          command: priority_only
      enabled: false
    - action: shell_command.ubuntu_pomodoro_on
      metadata: {}
      data: {}
    - action: shell_command.ubuntu_dnd_on
      metadata: {}
      data: {}
  description: ''
  icon: mdi:minus-circle
phone_do_not_disturb_off:
  alias: Home - DND - OFF
  sequence:
  - parallel:
    - action: shell_command.ubuntu_pomodoro_off
      metadata: {}
      data: {}
    - action: notify.mobile_app_pixel_9_pro
      metadata: {}
      data:
        message: command_dnd
        data:
          command: 'off'
      enabled: false
    - action: shell_command.ubuntu_dnd_off
      metadata: {}
      data: {}
    - action: shell_command.ubuntu_dnd_sync
      metadata: {}
      data: {}
  description: ''
  icon: mdi:minus-circle
games_sound_power:
  alias: Games Sound Power
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: power
games_sound_input:
  alias: Games Sound Input
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: input
games_sound_mute:
  alias: Games Sound Mute
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: mute
games_sound_volume_up:
  alias: Games Sound Volume Up
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: volume_up
games_sound_volume_down:
  alias: Games Sound Volume Down
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: volume_down
games_sound_level:
  alias: Games Sound Level
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: level
games_sound_effect:
  alias: Games Sound Effect
  sequence:
  - service: remote.send_command
    target:
      entity_id: remote.rf
    data:
      device: play_sound
      command: effect
control_pomodoro_on_or_cancel:
  alias: Control - Pomodoro - On Or Cancel
  sequence:
  - if:
    - condition: state
      entity_id: input_boolean.is_pomodoro_running
      state: 'on'
    then:
    - action: script.mode_pomodoro_off
      data: {}
    - action: script.speak
      metadata: {}
      data:
        message: El pomodoro fue cancelado
    else:
    - action: script.mode_pomodoro_on
      data: {}
  description: ''
  icon: mdi:clock-time-ten-outline
mode_pomodoro_off:
  alias: Mode - Pomodoro - OFF
  sequence:
  - action: input_boolean.turn_off
    metadata: {}
    data: {}
    target:
      entity_id: input_boolean.is_pomodoro_running
  icon: mdi:clock-time-eight-outline
  mode: single
  description: ''
speak:
  alias: Speak
  sequence:
  - parallel:
    - action: tts.cloud_say
      metadata: {}
      data:
        cache: true
        language: es-US
        entity_id: media_player.living_room_speakers
        message: '{{ message }}'
    - action: tts.cloud_say
      metadata: {}
      data:
        cache: true
        language: es-US
        entity_id: media_player.bedroom_speaker
        message: '{{ message }}'
    - action: tts.cloud_say
      metadata: {}
      data:
        cache: true
        language: es-US
        entity_id: media_player.games_speaker
        message: '{{ message }}'
  fields:
    message:
      selector:
        text:
      name: Message
      description: Message in spanish
      required: true
  description: ''
  icon: mdi:account-voice
active_area_lights_on:
  alias: Active Area - Lights ON
  sequence:
  - if:
    - condition: time
      after: 06:00:00
      before: '18:00:00'
    then:
    - stop: ''
    alias: Guard is daylight?
  - action: light.turn_off
    metadata: {}
    data: {}
    target:
      area_id:
      - balcony
      - main_bathroom
      - bedroom
      - clothes_zone
      - play_room
      - kitchen
      - living_room
      - studio
  - if:
    - condition: state
      entity_id: input_boolean.trabajo_switch
      state: 'on'
    then:
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        area_id: studio
    else:
    - action: light.turn_on
      metadata: {}
      data: {}
      target:
        entity_id: light.living_light_left
  description: ''
  icon: mdi:alarm-light
habitica_score_task_2:
  alias: Habitica - Score Task
  sequence:
  - event: HABITICA_SCORE_TASK
    event_data:
      task_name: '{{ task_name }}'
  fields:
    task_name:
      selector:
        text:
      name: Task Name
      description: Calls AppDaemon script to score the task with name
      required: true
  description: ''
  icon: mdi:checkbox-marked-circle-auto-outline
set_light_color_variable:
  alias: 'Light: set color from variable'
  description: Set a color variable based on the light's current color
  fields:
    light_entity:
      description: The light entity ID to check for color attributes
      example: light.living_room
    color_variable:
      description: The input_select entity ID to store the color
      example: input_select.light_color
  sequence:
  - choose:
    - conditions:
      - condition: template
        value_template: '{{ state_attr(light_entity, ''color_name'') != None }}'
      sequence:
      - target:
          entity_id: '{{ color_variable }}'
        data:
          option: '{{ state_attr(light_entity, ''color_name'') }}'
        action: input_select.select_option
    - conditions:
      - condition: template
        value_template: '{{ state_attr(light_entity, ''rgb_color'') != None }}'
      sequence:
      - target:
          entity_id: '{{ color_variable }}'
        data:
          option: '{{ ''rgb('' ~ state_attr(light_entity, ''rgb_color'')[0] ~ '',
            '' ~ state_attr(light_entity, ''rgb_color'')[1] ~ '', '' ~ state_attr(light_entity,
            ''rgb_color'')[2] ~ '')'' }}'
        action: input_select.select_option
    - conditions:
      - condition: template
        value_template: '{{ state_attr(light_entity, ''hs_color'') != None }}'
      sequence:
      - target:
          entity_id: '{{ color_variable }}'
        data:
          option: '{{ ''hs('' ~ state_attr(light_entity, ''hs_color'')[0] ~ '', ''
            ~ state_attr(light_entity, ''hs_color'')[1] ~ '')'' }}'
        action: input_select.select_option
habitica_create_task:
  alias: Habitica - Create Task
  sequence:
  - event: HABITICA_CREATE_TASK
    event_data:
      task_name: '{{ task_name }}'
      due_day_month: '{{ due_day_month }}'
      due_month_number: '{{ due_month_number }}'
      tag_name: '{{ tag_name }}'
  fields:
    task_name:
      selector:
        template: {}
      name: Task Name
      description: Calls AppDaemon script to create TODO task with label
      required: true
    tag_name:
      selector:
        select:
          options:
          - Homework
          - Study
      name: Tag name
      required: true
    due_day_month:
      selector:
        number:
          min: 1
          max: 31
      name: Due Day of Month
      required: true
      default: 1
    due_month_number:
      selector:
        number:
          min: 1
          max: 12
      name: due_month_number
  description: ''
  icon: mdi:checkbox-marked-circle-auto-outline
blink_light_living:
  alias: blink light - living
  use_blueprint:
    path: paulo-graca/script-blink-light.yaml
    input:
      light_selector:
        area_id:
        - living_room
        - balcony
        - kitchen
      delay_duration: 0.3
  description: ''
send_push_notification:
  sequence:
  - action: notify.mobile_app_pixel_9_pro
    metadata: {}
    data_template:
      title:
        '[object Object]':
      message: '{{message}}'
  fields:
    message:
      selector:
        text:
      name: Message
      required: true
    title:
      selector:
        text:
      name: Title
      required: true
  alias: Send Push Notification
  description: ''
money_register_money_expense_from_notification:
  sequence:
  - event: NOTIFICATION_HANDLER_TASK
    event_data:
      title: '{{ title }}'
      content: '{{ content }}'
      app: '{{ app }}'
  alias: Money - Register Money Expense from Notification
  fields:
    title:
      selector:
        text:
      name: title
      required: true
    content:
      selector:
        text:
      name: content
      required: true
    app:
      selector:
        text:
      name: app
      required: true
  description: ''
  icon: mdi:cash-register
notify_phone:
  sequence:
  - action: notify.mobile_app_pixel_9_pro
    data:
      title: '{{title}}'
      message: '{{message}}'
  fields:
    title:
      selector:
        text:
      name: Title
      required: true
    message:
      selector:
        text:
      name: Message
      required: true
  alias: notify phone
  description: ''
