import appdaemon.plugins.hass.hassapi as hass
import os
import requests
import json

class OpenAIExample(hass.Hass):
    """
    Example AppDaemon app that uses OpenAI API
    """
    
    def initialize(self):
        """Initialize the app"""
        self.log("OpenAI Example App initialized")
        
        # Get the OpenAI API key from environment variables
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if not self.openai_api_key:
            self.log("ERROR: OPENAI_API_KEY environment variable not found!", level="ERROR")
            return
        
        self.log("OpenAI API key loaded successfully")
        
        # Example: Listen for a specific event or state change
        # self.listen_state(self.on_light_change, "light.living_room")
        
        # Example: Register a service that can be called from Home Assistant
        self.register_service("openai_example/ask_question", self.ask_openai_question)
    
    def ask_openai_question(self, namespace, domain, service, kwargs):
        """
        Service that can be called from Home Assistant to ask OpenAI a question
        Usage in Home Assistant:
        service: appdaemon.openai_example_ask_question
        data:
          question: "What's the weather like today?"
        """
        question = kwargs.get('question', 'Hello, how are you?')
        
        try:
            response = self.call_openai_api(question)
            self.log(f"OpenAI Response: {response}")
            
            # You can use the response to trigger other actions
            # For example, send a notification or update an entity
            self.call_service("notify/persistent_notification", 
                            title="OpenAI Response",
                            message=response)
            
        except Exception as e:
            self.log(f"Error calling OpenAI API: {str(e)}", level="ERROR")
    
    def call_openai_api(self, prompt):
        """
        Make a call to OpenAI API
        """
        headers = {
            'Authorization': f'Bearer {self.openai_api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'gpt-3.5-turbo',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 150,
            'temperature': 0.7
        }
        
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content'].strip()
        else:
            raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
    
    def on_light_change(self, entity, attribute, old, new, kwargs):
        """
        Example callback for when a light state changes
        """
        if new == "on":
            question = f"The {entity} light was just turned on. Give me a fun fact about lighting."
            try:
                response = self.call_openai_api(question)
                self.log(f"Fun fact: {response}")
            except Exception as e:
                self.log(f"Error getting fun fact: {str(e)}", level="ERROR")
