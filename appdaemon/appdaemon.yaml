---
# AppDaemon Configuration
secrets: /config/secrets.yaml
appdaemon:
  latitude: !secret latitude
  longitude: !secret longitude
  elevation: !secret elevation
  time_zone: !secret time_zone
  plugins:
    HASS:
      type: hass
      ha_url: http://supervisor/core
      token: !secret appdaemon_token
  
# HTTP Configuration
http:
  url: http://127.0.0.1:5050

# Admin Interface
admin:
  title: AppDaemon
  stats_update: realtime

# API Configuration  
api:
  port: 5050

# Hadashboard (optional)
hadashboard:
  dash_url: http://127.0.0.1:5050
  dash_dir: /config/appdaemon/dashboards

# Logging
logs:
  main_log:
    filename: /config/appdaemon/logs/appdaemon.log
    log_generations: 3
    log_size: 1000000
  error_log:
    filename: /config/appdaemon/logs/error.log
    log_generations: 3
    log_size: 1000000
  access_log:
    filename: /config/appdaemon/logs/access.log
    log_generations: 3
    log_size: 1000000
  diag_log:
    filename: /config/appdaemon/logs/diag.log
    log_generations: 3
    log_size: 1000000

# Environment Variables for Apps
env_vars:
  OPENAI_API_KEY: !secret openai_api_key
